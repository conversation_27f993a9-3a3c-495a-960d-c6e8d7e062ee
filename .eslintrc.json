{"env": {"browser": true, "es2022": true, "node": true}, "extends": ["eslint:recommended"], "plugins": ["compat"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-undef": "error", "no-unused-vars": "warn", "no-console": "off", "no-debugger": "error", "indent": "off", "quotes": "off", "semi": "off", "max-len": "off", "prefer-const": "off", "prefer-template": "off", "prefer-arrow-callback": "off", "object-shorthand": "off", "no-var": "off", "compat/compat": "off", "no-case-declarations": "off", "no-useless-escape": "off"}, "globals": {"gtag": "readonly", "module": "readonly"}, "settings": {"polyfills": ["Promise", "fetch", "WebSocket", "IntersectionObserver", "ResizeObserver"]}, "overrides": [{"files": ["backend/**/*.js"], "env": {"node": true, "browser": false}, "rules": {"no-console": "off", "compat/compat": "off"}}, {"files": ["projects/**/*.js"], "env": {"browser": true, "node": false}, "globals": {"Chart": "readonly", "THREE": "readonly", "SimplePeer": "readonly", "SudokuSolver": "readonly", "generateText": "readonly", "getProjectData": "readonly", "generatePlaceholderImage": "readonly", "GALLERY_DATA": "readonly", "gtag": "readonly"}}, {"files": ["**/sw.js", "**/service-worker.js"], "env": {"serviceworker": true}, "globals": {"clients": "readonly"}}]}