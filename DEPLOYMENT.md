# 🚀 Deployment Guide

This guide provides step-by-step instructions for deploying the portfolio and backend APIs to production.

## 📋 Prerequisites

- Node.js 18+ installed
- Git repository access
- Domain name (optional but recommended)
- Basic understanding of command line

## 🌐 Frontend Deployment (GitHub Pages)

### Option 1: GitHub Pages (Free)

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Portfolio ready for deployment"
   git push origin main
   ```

2. **Enable GitHub Pages**:
   - Go to repository Settings
   - Scroll to "Pages" section
   - Select "Deploy from a branch"
   - Choose "main" branch and "/ (root)" folder
   - Click "Save"

3. **Access your site**:
   - URL: `https://yourusername.github.io/PortFolio-2025/`
   - Updates automatically on push to main branch

### Option 2: Netlify (Free Tier)

1. **Connect Repository**:
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository

2. **Build Settings**:
   - Build command: `npm run build` (if needed)
   - Publish directory: `/` (root)
   - Node version: 18

3. **Deploy**:
   - <PERSON><PERSON> will auto-deploy on every push
   - Custom domain available in settings

## 🖥️ Backend Deployment

### Option 1: Railway (Recommended for beginners)

1. **Prepare for deployment**:
   ```bash
   cd backend/wordle-api
   npm install
   ```

2. **Create railway.json**:
   ```json
   {
     "$schema": "https://railway.app/railway.schema.json",
     "build": {
       "builder": "NIXPACKS"
     },
     "deploy": {
       "startCommand": "npm start",
       "healthcheckPath": "/health"
     }
   }
   ```

3. **Deploy to Railway**:
   - Install Railway CLI: `npm install -g @railway/cli`
   - Login: `railway login`
   - Deploy: `railway up`

### Option 2: Heroku

1. **Install Heroku CLI**:
   ```bash
   npm install -g heroku
   heroku login
   ```

2. **Create Heroku app**:
   ```bash
   cd backend/wordle-api
   heroku create your-wordle-api
   ```

3. **Configure environment**:
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set PORT=3001
   ```

4. **Deploy**:
   ```bash
   git add .
   git commit -m "Deploy to Heroku"
   git push heroku main
   ```

### Option 3: DigitalOcean App Platform

1. **Create app.yaml**:
   ```yaml
   name: wordle-api
   services:
   - name: api
     source_dir: backend/wordle-api
     github:
       repo: yourusername/PortFolio-2025
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     http_port: 3001
     health_check:
       http_path: /health
   ```

2. **Deploy via DigitalOcean dashboard**:
   - Connect GitHub repository
   - Select app.yaml configuration
   - Deploy

## 🔧 Environment Configuration

### Backend Environment Variables

Create `.env` files for each backend service:

**backend/wordle-api/.env**:
```env
NODE_ENV=production
PORT=3001
DATABASE_URL=sqlite:./data/wordle.db
CORS_ORIGIN=https://yourdomain.com
```

**backend/typing-api/.env**:
```env
NODE_ENV=production
PORT=3002
DATABASE_URL=sqlite:./data/typing.db
CORS_ORIGIN=https://yourdomain.com
```

### Frontend Configuration

Update API URLs in frontend projects:

**projects/wordle-game/main.js**:
```javascript
// Change from localhost to production URL
this.apiBaseUrl = 'https://your-wordle-api.railway.app/api/v2/wordle';
```

## 🗄️ Database Setup

### SQLite (Development/Small Scale)
- Databases are created automatically
- Files stored in `backend/*/data/` directories
- Backup regularly for production

### PostgreSQL (Production Recommended)

1. **Get PostgreSQL database**:
   - Railway: Automatically provisions
   - Heroku: `heroku addons:create heroku-postgresql:hobby-dev`
   - DigitalOcean: Create managed database

2. **Update connection string**:
   ```env
   DATABASE_URL=postgresql://user:password@host:port/database
   ```

3. **Update database code**:
   ```javascript
   // Replace SQLite with PostgreSQL client
   const { Pool } = require('pg');
   const pool = new Pool({
     connectionString: process.env.DATABASE_URL,
     ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
   });
   ```

## 🔒 Security Checklist

### Backend Security
- [ ] Environment variables set correctly
- [ ] CORS configured for your domain only
- [ ] Rate limiting enabled
- [ ] Input validation on all endpoints
- [ ] HTTPS enforced
- [ ] Database credentials secured

### Frontend Security
- [ ] No sensitive data in client code
- [ ] API URLs updated for production
- [ ] Content Security Policy headers
- [ ] HTTPS redirect enabled

## 📊 Monitoring & Analytics

### Basic Monitoring
1. **Health checks**: All APIs have `/health` endpoints
2. **Error logging**: Console errors logged in production
3. **Analytics**: Google Analytics integrated (update tracking ID)

### Advanced Monitoring (Optional)
- **Sentry**: Error tracking and performance monitoring
- **LogRocket**: Session replay and debugging
- **Uptime monitoring**: Services like UptimeRobot

## 🚀 Deployment Checklist

### Pre-deployment
- [ ] All tests passing (`npm test`)
- [ ] Code linted and formatted
- [ ] Environment variables configured
- [ ] Database migrations ready
- [ ] API URLs updated in frontend

### Post-deployment
- [ ] Health checks responding
- [ ] Frontend loads correctly
- [ ] API endpoints working
- [ ] Database connections established
- [ ] Analytics tracking
- [ ] Error monitoring active

## 🔄 CI/CD Pipeline (Advanced)

### GitHub Actions Example

Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy Portfolio

on:
  push:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: '18'
    - run: npm install
    - run: npm test

  deploy-frontend:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Deploy to Railway
      run: |
        npm install -g @railway/cli
        railway up --service wordle-api
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
```

## 🆘 Troubleshooting

### Common Issues

1. **CORS Errors**:
   - Update CORS_ORIGIN environment variable
   - Check API URL in frontend code

2. **Database Connection Failed**:
   - Verify DATABASE_URL format
   - Check database service status
   - Ensure SSL settings correct

3. **Build Failures**:
   - Check Node.js version compatibility
   - Verify all dependencies installed
   - Review build logs for specific errors

4. **404 Errors**:
   - Check routing configuration
   - Verify file paths and URLs
   - Ensure static files served correctly

### Getting Help
- Check deployment platform documentation
- Review application logs
- Test locally first
- Use health check endpoints for debugging

## 📈 Scaling Considerations

### When to Scale
- Response times > 2 seconds
- Error rates > 1%
- CPU usage consistently > 80%
- Memory usage > 90%

### Scaling Options
1. **Vertical scaling**: Increase instance size
2. **Horizontal scaling**: Add more instances
3. **Database optimization**: Indexes, query optimization
4. **CDN**: Static asset delivery
5. **Caching**: Redis for session/data caching

---

**Note**: This portfolio is designed for learning and demonstration. For production applications, consider additional security measures, monitoring, and infrastructure as code practices.
