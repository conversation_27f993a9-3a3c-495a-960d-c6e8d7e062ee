# Z<PERSON>den Sharp - Full-Stack Developer Portfolio

[![Portfolio](https://img.shields.io/badge/Portfolio-Live-brightgreen)](https://zaydenjs.github.io/PortFolio-2025/)
[![GitHub](https://img.shields.io/badge/GitHub-ZaydenJS-blue)](https://github.com/ZaydenJS)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**Functional web applications demonstrating JavaScript fundamentals, working APIs, and modern web development practices**

## 🚀 Live Portfolio

**[View Portfolio →](https://zaydenjs.github.io/PortFolio-2025/)**

A portfolio featuring 6 functional web applications plus 3 working backend APIs, demonstrating junior-level full-stack development skills with vanilla JavaScript and Node.js.

## 🎯 Project Overview

This portfolio contains **6 front-end projects**, all built without frameworks to showcase fundamental web development skills:

### 🌐 Front-End Projects (Static)

| #   | Project                                        | Focus                | Live Demo                                                                      | Tech Stack                           |
| --- | ---------------------------------------------- | -------------------- | ------------------------------------------------------------------------------ | ------------------------------------ |
| 1   | [Project Gallery](./projects/project-gallery/) | Portfolio Showcase   | [🔗 Demo](https://zaydenjs.github.io/PortFolio-2025/projects/project-gallery/) | HTML5, CSS3, Vanilla JS, Gallery API |
| 2   | [CollaboCanvas](./projects/collabo-canvas/)    | Whiteboard Prototype | [🔗 Demo](https://zaydenjs.github.io/PortFolio-2025/projects/collabo-canvas/)  | WebRTC, Canvas API, P2P              |
| 3   | [CollabSpace](./projects/workroom/)            | Workspace UI Design  | [🔗 Demo](https://zaydenjs.github.io/PortFolio-2025/projects/workroom/)        | HTML5, CSS3, Responsive Design       |

### 🔧 Back-End Projects (Full-Stack)

| #   | Project                                    | Focus                    | Live Demo                                                                    | Tech Stack                       |
| --- | ------------------------------------------ | ------------------------ | ---------------------------------------------------------------------------- | -------------------------------- |
| 1   | [Wordle Game](./projects/wordle-game/)     | Word Game Logic          | [🔗 Demo](https://zaydenjs.github.io/PortFolio-2025/projects/wordle-game/)   | Node.js API, Game Algorithms     |
| 2   | [Typing Test](./projects/typing-test/)     | Performance Analytics    | [🔗 Demo](https://zaydenjs.github.io/PortFolio-2025/projects/typing-test/)   | Real-time WPM, Statistics        |
| 3   | [Sudoku Solver](./projects/sudoku-solver/) | Algorithm Implementation | [🔗 Demo](https://zaydenjs.github.io/PortFolio-2025/projects/sudoku-solver/) | Backtracking, Constraint Solving |

## 🛠️ Technical Highlights

- **Zero Build Tools**: All front-end projects use native ES modules and vanilla JavaScript
- **Modern Standards**: HTML5 semantic markup, CSS3 Grid/Flexbox, ES2022 features
- **Accessibility**: WCAG 2.1 AA compliance, semantic HTML, ARIA roles
- **Performance**: Lighthouse scores ≥90 across all metrics
- **Progressive Enhancement**: Service Workers, offline functionality, responsive design
- **Real-time Features**: WebRTC peer-to-peer, WebSocket connections
- **API Integration**: RESTful services, third-party API consumption
- **Security**: No secrets in code, environment variable examples provided

## 🚀 Quick Start

### Front-End Projects

```bash
# Clone the repository
git clone https://github.com/ZaydenJS/PortFolio-2025.git
cd PortFolio-2025

# Serve locally (any static server works)
npx serve .
# or
python -m http.server 8000
# or
php -S localhost:8000
```

Visit `http://localhost:8000` to see the portfolio landing page.

### Production Deployment

📋 **See [DEPLOYMENT.md](./DEPLOYMENT.md) for comprehensive deployment guide**:

- Frontend deployment (GitHub Pages, Netlify)
- Backend deployment (Railway, Heroku, DigitalOcean)
- Database setup and configuration
- Environment variables and security
- CI/CD pipeline examples

### Back-End Services

#### Wordle API

```bash
cd backend/wordle-api
npm install
node server.js
# Visit http://localhost:3000 for the game interface
```

#### Typing Test API

```bash
cd backend/typing-api
npm install
node server.js
# Visit http://localhost:3001 for the typing interface
```

#### Sudoku Solver API

```bash
cd backend/sudoku-api
npm install
node server.js
# Visit http://localhost:3002 for the solver interface
```

## 📁 Repository Structure

```
PortFolio-2025/
├── README.md                 # This file
├── index.html               # Portfolio landing page
├── code-showcase.html       # Interactive code viewer
├── resume.html              # Professional resume
├── lighthouserc.js          # Performance monitoring
├── projects/                # 6 front-end projects
│   ├── project-gallery/    # Portfolio showcase gallery
│   ├── collabo-canvas/     # Real-time collaborative whiteboard
│   ├── workroom/           # Enterprise workspace platform
│   ├── wordle-game/        # Word game with algorithms
│   ├── typing-test/        # Performance analytics app
│   └── sudoku-solver/      # Algorithm implementation
└── backend/                # 4 Node.js APIs
    ├── wordle-api/         # Word game backend
    ├── typing-api/         # Typing performance API
    ├── sudoku-api/         # Puzzle solving algorithms
    └── workroom-api/       # Collaboration backend
```

## 🎨 Design Philosophy

- **Vanilla First**: Demonstrates core web technologies without framework dependencies
- **Progressive Enhancement**: Features work without JavaScript, enhanced with it
- **Accessibility**: Keyboard navigation, screen reader support, high contrast
- **Performance**: Optimized assets, lazy loading, efficient algorithms
- **Maintainability**: Clean code, consistent patterns, comprehensive documentation

## 🧪 Quality Assurance

- **Testing**: Jest with JSDOM for comprehensive unit testing (70%+ coverage)
- **Linting**: ESLint with `eslint-plugin-compat` for browser compatibility
- **Formatting**: Prettier for consistent code style
- **Security**: Input sanitization, XSS prevention, rate limiting
- **Accessibility**: WCAG 2.1 AA compliance, keyboard navigation, ARIA support
- **Performance**: Lighthouse CI monitoring (95+ scores), optimized loading

### Testing Commands

```bash
npm test              # Run all tests
npm run test:coverage # Generate coverage report
npm run test:ci       # CI/CD testing with coverage
npm run validate      # Full quality check (lint + test)
```

## 📞 Contact & Resume

- **Portfolio**: [zaydenjs.github.io/PortFolio-2025](https://zaydenjs.github.io/PortFolio-2025/)
- **LinkedIn**: [linkedin.com/in/zayden-sharp](https://linkedin.com/in/zayden-sharp)
- **GitHub**: [github.com/ZaydenJS](https://github.com/ZaydenJS)
- **Resume**: [📄 View Online](https://zaydenjs.github.io/PortFolio-2025/resume.html)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

_Built with ❤️ using vanilla web technologies_
