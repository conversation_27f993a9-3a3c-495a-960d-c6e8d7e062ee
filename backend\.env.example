# Backend APIs Environment Variables Template
# Copy this file to .env and fill in your actual values

# IMPORTANT: Never commit .env files to version control!
# Add .env to your .gitignore file

# ===========================================
# SECURITY CONFIGURATION (REQUIRED)
# ===========================================

# JWT Secret - MUST be changed in production
# Generate a strong secret: openssl rand -base64 32
JWT_SECRET=your-super-secure-jwt-secret-key-here

# ===========================================
# SERVER CONFIGURATION
# ===========================================

# Environment
NODE_ENV=development

# Server Ports
WORDLE_API_PORT=3001
SUDOKU_API_PORT=3002
TYPING_API_PORT=3003
WORKROOM_API_PORT=3006

# ===========================================
# DATABASE CONFIGURATION
# ===========================================

# SQLite Database Paths (for development)
WORDLE_DB_PATH=./data/wordle.db
SUDOKU_DB_PATH=./data/sudoku.db
TYPING_DB_PATH=./data/typing.db
WORKROOM_DB_PATH=./data/workspace.db

# PostgreSQL (for production)
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# ===========================================
# REDIS CONFIGURATION (Optional)
# ===========================================

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379

# ===========================================
# CORS CONFIGURATION
# ===========================================

# Allowed origins (comma-separated)
CORS_ORIGIN=http://localhost:3000,https://yourdomain.com

# ===========================================
# RATE LIMITING
# ===========================================

RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# ===========================================
# LOGGING
# ===========================================

LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_FILES=14
LOG_MAX_SIZE=20m

# ===========================================
# PRODUCTION DEPLOYMENT
# ===========================================

# For production deployment, ensure:
# 1. JWT_SECRET is a strong, unique secret
# 2. NODE_ENV=production
# 3. Use proper database URLs
# 4. Configure CORS_ORIGIN for your domain
# 5. Set appropriate rate limits
# 6. Use HTTPS in production
