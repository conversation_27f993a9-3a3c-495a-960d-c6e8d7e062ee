version: '3.8'

services:
  sudoku-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - '3002:3002'
    environment:
      - NODE_ENV=production
      - PORT=3002
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=sqlite:///app/data/sudoku.db
      - LOG_LEVEL=info
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - JWT_SECRET=${JWT_SECRET:-please-set-jwt-secret-in-env}
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - sudoku_data:/app/data
      - sudoku_logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'node', 'dist/health-check.js']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 3s
      retries: 3

  prometheus:
    image: prom/prometheus:latest
    ports:
      - '9090:9090'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - '3001:3000'
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

volumes:
  sudoku_data:
  sudoku_logs:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: sudoku-network
