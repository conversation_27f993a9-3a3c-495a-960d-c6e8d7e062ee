/**
 * Simple Sudoku API Server
 * A working implementation of the Sudoku solver API
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Sudoku solver implementation
class SudokuSolver {
  constructor() {
    this.size = 9;
    this.boxSize = 3;
  }

  isValid(board, row, col, num) {
    // Check row
    for (let x = 0; x < this.size; x++) {
      if (board[row][x] === num) return false;
    }

    // Check column
    for (let x = 0; x < this.size; x++) {
      if (board[x][col] === num) return false;
    }

    // Check 3x3 box
    const startRow = row - (row % this.boxSize);
    const startCol = col - (col % this.boxSize);
    for (let i = 0; i < this.boxSize; i++) {
      for (let j = 0; j < this.boxSize; j++) {
        if (board[i + startRow][j + startCol] === num) return false;
      }
    }

    return true;
  }

  solve(board) {
    for (let row = 0; row < this.size; row++) {
      for (let col = 0; col < this.size; col++) {
        if (board[row][col] === 0) {
          for (let num = 1; num <= 9; num++) {
            if (this.isValid(board, row, col, num)) {
              board[row][col] = num;
              if (this.solve(board)) return true;
              board[row][col] = 0;
            }
          }
          return false;
        }
      }
    }
    return true;
  }

  validate(board) {
    // Check if the board is valid (no conflicts)
    for (let row = 0; row < this.size; row++) {
      for (let col = 0; col < this.size; col++) {
        if (board[row][col] !== 0) {
          const num = board[row][col];
          board[row][col] = 0; // Temporarily remove to check
          if (!this.isValid(board, row, col, num)) {
            board[row][col] = num; // Restore
            return false;
          }
          board[row][col] = num; // Restore
        }
      }
    }
    return true;
  }

  generate(difficulty = 'medium') {
    // Create a solved board
    const board = Array(9).fill().map(() => Array(9).fill(0));
    this.solve(board);

    // Remove numbers based on difficulty
    const cellsToRemove = {
      easy: 40,
      medium: 50,
      hard: 60
    };

    const toRemove = cellsToRemove[difficulty] || 50;
    let removed = 0;

    while (removed < toRemove) {
      const row = Math.floor(Math.random() * 9);
      const col = Math.floor(Math.random() * 9);
      
      if (board[row][col] !== 0) {
        board[row][col] = 0;
        removed++;
      }
    }

    return board;
  }

  getDifficulty(board) {
    const filledCells = board.flat().filter(cell => cell !== 0).length;
    if (filledCells >= 50) return 'easy';
    if (filledCells >= 40) return 'medium';
    return 'hard';
  }
}

const solver = new SudokuSolver();

// Routes
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'sudoku-api',
    version: '2.0.0'
  });
});

app.post('/api/v2/sudoku/solve', (req, res) => {
  try {
    const { puzzle } = req.body;

    if (!puzzle || !Array.isArray(puzzle) || puzzle.length !== 9) {
      return res.status(400).json({ 
        error: 'Invalid puzzle format. Expected 9x9 array.' 
      });
    }

    // Validate puzzle format
    for (let row of puzzle) {
      if (!Array.isArray(row) || row.length !== 9) {
        return res.status(400).json({ 
          error: 'Invalid puzzle format. Each row must be an array of 9 numbers.' 
        });
      }
      for (let cell of row) {
        if (!Number.isInteger(cell) || cell < 0 || cell > 9) {
          return res.status(400).json({ 
            error: 'Invalid cell value. Must be integer between 0-9.' 
          });
        }
      }
    }

    // Create a copy to solve
    const puzzleCopy = puzzle.map(row => [...row]);

    // Validate the puzzle
    if (!solver.validate(puzzleCopy)) {
      return res.status(400).json({ 
        error: 'Invalid puzzle. Contains conflicts.' 
      });
    }

    // Solve the puzzle
    const startTime = Date.now();
    const solved = solver.solve(puzzleCopy);
    const solveTime = Date.now() - startTime;

    if (solved) {
      res.json({
        success: true,
        solution: puzzleCopy,
        original: puzzle,
        difficulty: solver.getDifficulty(puzzle),
        solveTime: `${solveTime}ms`,
        algorithm: 'backtracking'
      });
    } else {
      res.status(400).json({ 
        error: 'Puzzle has no solution.' 
      });
    }
  } catch (error) {
    console.error('Solve error:', error);
    res.status(500).json({ 
      error: 'Internal server error while solving puzzle.' 
    });
  }
});

app.post('/api/v2/sudoku/validate', (req, res) => {
  try {
    const { puzzle } = req.body;

    if (!puzzle || !Array.isArray(puzzle) || puzzle.length !== 9) {
      return res.status(400).json({ 
        error: 'Invalid puzzle format. Expected 9x9 array.' 
      });
    }

    const puzzleCopy = puzzle.map(row => [...row]);
    const isValid = solver.validate(puzzleCopy);

    res.json({
      valid: isValid,
      difficulty: isValid ? solver.getDifficulty(puzzle) : null,
      filledCells: puzzle.flat().filter(cell => cell !== 0).length
    });
  } catch (error) {
    console.error('Validation error:', error);
    res.status(500).json({ 
      error: 'Internal server error while validating puzzle.' 
    });
  }
});

app.get('/api/v2/sudoku/generate/:difficulty?', (req, res) => {
  try {
    const difficulty = req.params.difficulty || 'medium';
    
    if (!['easy', 'medium', 'hard'].includes(difficulty)) {
      return res.status(400).json({ 
        error: 'Invalid difficulty. Must be easy, medium, or hard.' 
      });
    }

    const puzzle = solver.generate(difficulty);
    
    res.json({
      puzzle,
      difficulty,
      filledCells: puzzle.flat().filter(cell => cell !== 0).length,
      emptyCells: puzzle.flat().filter(cell => cell === 0).length,
      generated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Generation error:', error);
    res.status(500).json({ 
      error: 'Internal server error while generating puzzle.' 
    });
  }
});

app.get('/api/v2/sudoku/info', (req, res) => {
  res.json({
    service: 'Sudoku API',
    version: '2.0.0',
    algorithms: ['backtracking'],
    difficulties: ['easy', 'medium', 'hard'],
    endpoints: {
      solve: 'POST /api/v2/sudoku/solve',
      validate: 'POST /api/v2/sudoku/validate',
      generate: 'GET /api/v2/sudoku/generate/:difficulty'
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Sudoku API server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`API Info: http://localhost:${PORT}/api/v2/sudoku/info`);
});

module.exports = app;
