# Wordle API - Deployment Guide

## Quick Start (Local Development)

1. Install dependencies:
```bash
npm install
```

2. Start the server:
```bash
npm start
```

3. Test the API:
```bash
curl http://localhost:3001/health
```

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Game Management
- `GET /api/game/new` - Create a new game
- `POST /api/game/:gameId/guess` - Submit a guess
- `GET /api/game/:gameId` - Get game state
- `GET /api/stats` - Get game statistics

## Example Usage

```javascript
// Create a new game
const response = await fetch('http://localhost:3001/api/game/new');
const game = await response.json();

// Make a guess
const guessResponse = await fetch(`http://localhost:3001/api/game/${game.gameId}/guess`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ guess: 'ABOUT' })
});
const result = await guessResponse.json();
```

## Deployment Options

### Heroku
1. Create a Heroku app
2. Set environment variables
3. Deploy with Git

### Railway
1. Connect GitHub repository
2. Set PORT environment variable
3. Deploy automatically

### Vercel
1. Install Vercel CLI
2. Run `vercel` in project directory
3. Follow deployment prompts

## Environment Variables

- `PORT` - Server port (default: 3001)
- `NODE_ENV` - Environment (development/production)

## Features

- ✅ Working Wordle game logic
- ✅ Word validation
- ✅ Game state management
- ✅ Statistics tracking
- ✅ Rate limiting
- ✅ Security headers
- ✅ CORS support
- ✅ Health checks

## Note

This is a simplified but fully functional version of the Wordle API. It demonstrates core backend development skills including:
- RESTful API design
- Input validation
- Game logic implementation
- Error handling
- Security best practices
