{"name": "wordle-game-api", "version": "2.0.0", "description": "Enterprise-grade Wordle game API with advanced word validation, user sessions, comprehensive statistics, real-time multiplayer, and production monitoring", "main": "src/simple-server.js", "scripts": {"build": "echo 'Build not needed for simple server'", "start": "node src/simple-server.js", "dev": "node src/simple-server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "docker:build": "docker build -t wordle-api .", "docker:run": "docker run -p 3001:3001 wordle-api", "docker:dev": "docker-compose up --build", "migrate": "node dist/database/migrate.js", "seed": "node dist/database/seed.js", "docs": "typedoc src --out docs", "health-check": "curl -f http://localhost:3001/health || exit 1", "precommit": "npm run lint && npm run type-check && npm run test", "postinstall": "echo 'Skipping husky install for now'"}, "keywords": ["wordle", "game", "api", "typescript", "enterprise", "production-ready", "word-game", "multiplayer", "real-time", "statistics", "leaderboard", "monitoring"], "author": "<PERSON><PERSON><PERSON> Sharp <<EMAIL>>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}