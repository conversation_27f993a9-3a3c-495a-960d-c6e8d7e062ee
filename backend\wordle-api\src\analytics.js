/**
 * Analytics Service - Real Game Analytics and Performance Metrics
 * Provides actual insights into game performance and user behavior
 */

class AnalyticsService {
  constructor(databaseService) {
    this.db = databaseService;
    this.metrics = {
      requests: 0,
      errors: 0,
      responseTime: [],
      gameCreations: 0,
      guessesProcessed: 0,
      startTime: Date.now()
    };
  }

  /**
   * Track API request metrics
   */
  trackRequest(endpoint, responseTime, statusCode) {
    this.metrics.requests++;
    this.metrics.responseTime.push(responseTime);
    
    if (statusCode >= 400) {
      this.metrics.errors++;
    }

    // Keep only last 1000 response times for memory efficiency
    if (this.metrics.responseTime.length > 1000) {
      this.metrics.responseTime = this.metrics.responseTime.slice(-1000);
    }
  }

  /**
   * Track game creation
   */
  trackGameCreation(difficulty, isDaily = false) {
    this.metrics.gameCreations++;
    // Could extend to track difficulty distribution, daily vs random games, etc.
  }

  /**
   * Track guess processing
   */
  trackGuess(gameId, guess, result, responseTime) {
    this.metrics.guessesProcessed++;
    // Could extend to track common guesses, success patterns, etc.
  }

  /**
   * Get real-time performance metrics
   */
  getPerformanceMetrics() {
    const uptime = Date.now() - this.metrics.startTime;
    const avgResponseTime = this.metrics.responseTime.length > 0 
      ? this.metrics.responseTime.reduce((a, b) => a + b, 0) / this.metrics.responseTime.length 
      : 0;

    return {
      uptime: Math.floor(uptime / 1000), // seconds
      totalRequests: this.metrics.requests,
      totalErrors: this.metrics.errors,
      errorRate: this.metrics.requests > 0 ? ((this.metrics.errors / this.metrics.requests) * 100).toFixed(2) : '0.00',
      averageResponseTime: Math.round(avgResponseTime),
      requestsPerMinute: this.metrics.requests > 0 ? Math.round((this.metrics.requests / (uptime / 60000))) : 0,
      gameCreations: this.metrics.gameCreations,
      guessesProcessed: this.metrics.guessesProcessed,
      memoryUsage: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get game analytics from database
   */
  async getGameAnalytics() {
    try {
      const analytics = await this.db.db.all(`
        SELECT 
          COUNT(*) as total_games,
          COUNT(CASE WHEN status = 'won' THEN 1 END) as games_won,
          COUNT(CASE WHEN status = 'lost' THEN 1 END) as games_lost,
          COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as games_in_progress,
          AVG(CASE WHEN duration IS NOT NULL THEN duration END) as avg_game_duration,
          COUNT(CASE WHEN difficulty = 'easy' THEN 1 END) as easy_games,
          COUNT(CASE WHEN difficulty = 'medium' THEN 1 END) as medium_games,
          COUNT(CASE WHEN difficulty = 'hard' THEN 1 END) as hard_games,
          COUNT(CASE WHEN is_daily = 1 THEN 1 END) as daily_games,
          MIN(start_time) as first_game,
          MAX(start_time) as last_game
        FROM games
      `);

      const guessAnalytics = await this.db.db.all(`
        SELECT 
          COUNT(*) as total_guesses,
          AVG(guess_number) as avg_guesses_per_game,
          COUNT(DISTINCT game_id) as games_with_guesses
        FROM guesses
      `);

      const popularWords = await this.db.db.all(`
        SELECT guess_word, COUNT(*) as frequency
        FROM guesses 
        GROUP BY guess_word 
        ORDER BY frequency DESC 
        LIMIT 10
      `);

      const difficultyStats = await this.db.db.all(`
        SELECT 
          g.difficulty,
          COUNT(*) as games,
          COUNT(CASE WHEN g.status = 'won' THEN 1 END) as wins,
          AVG(CASE WHEN g.status = 'won' THEN g.current_guess END) as avg_guesses_to_win,
          AVG(CASE WHEN g.duration IS NOT NULL THEN g.duration END) as avg_duration
        FROM games g
        GROUP BY g.difficulty
      `);

      return {
        overview: analytics[0] || {},
        guesses: guessAnalytics[0] || {},
        popularWords: popularWords || [],
        difficultyBreakdown: difficultyStats || [],
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting game analytics:', error);
      return {
        overview: {},
        guesses: {},
        popularWords: [],
        difficultyBreakdown: [],
        error: 'Failed to retrieve analytics',
        generatedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Get time-based analytics (games per hour, day, etc.)
   */
  async getTimeAnalytics() {
    try {
      const hourlyStats = await this.db.db.all(`
        SELECT 
          strftime('%H', start_time) as hour,
          COUNT(*) as games_started,
          COUNT(CASE WHEN status = 'won' THEN 1 END) as games_won
        FROM games 
        WHERE start_time >= datetime('now', '-24 hours')
        GROUP BY strftime('%H', start_time)
        ORDER BY hour
      `);

      const dailyStats = await this.db.db.all(`
        SELECT 
          DATE(start_time) as date,
          COUNT(*) as games_started,
          COUNT(CASE WHEN status = 'won' THEN 1 END) as games_won,
          COUNT(DISTINCT CASE WHEN is_daily = 1 THEN target_word END) as unique_daily_words
        FROM games 
        WHERE start_time >= datetime('now', '-7 days')
        GROUP BY DATE(start_time)
        ORDER BY date DESC
      `);

      return {
        hourly: hourlyStats || [],
        daily: dailyStats || [],
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting time analytics:', error);
      return {
        hourly: [],
        daily: [],
        error: 'Failed to retrieve time analytics',
        generatedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Get word analytics
   */
  async getWordAnalytics() {
    try {
      const wordStats = await this.db.db.all(`
        SELECT 
          target_word,
          COUNT(*) as times_used,
          COUNT(CASE WHEN status = 'won' THEN 1 END) as times_won,
          AVG(CASE WHEN status = 'won' THEN current_guess END) as avg_guesses_to_solve,
          difficulty
        FROM games 
        WHERE status IN ('won', 'lost')
        GROUP BY target_word, difficulty
        HAVING COUNT(*) > 1
        ORDER BY times_used DESC
        LIMIT 20
      `);

      const hardestWords = await this.db.db.all(`
        SELECT 
          target_word,
          difficulty,
          COUNT(*) as attempts,
          COUNT(CASE WHEN status = 'won' THEN 1 END) as wins,
          ROUND((COUNT(CASE WHEN status = 'won' THEN 1 END) * 100.0 / COUNT(*)), 1) as win_rate
        FROM games 
        WHERE status IN ('won', 'lost')
        GROUP BY target_word, difficulty
        HAVING COUNT(*) >= 3
        ORDER BY win_rate ASC, attempts DESC
        LIMIT 10
      `);

      const easiestWords = await this.db.db.all(`
        SELECT 
          target_word,
          difficulty,
          COUNT(*) as attempts,
          COUNT(CASE WHEN status = 'won' THEN 1 END) as wins,
          ROUND((COUNT(CASE WHEN status = 'won' THEN 1 END) * 100.0 / COUNT(*)), 1) as win_rate,
          AVG(CASE WHEN status = 'won' THEN current_guess END) as avg_guesses
        FROM games 
        WHERE status IN ('won', 'lost')
        GROUP BY target_word, difficulty
        HAVING COUNT(*) >= 3 AND win_rate > 80
        ORDER BY avg_guesses ASC, win_rate DESC
        LIMIT 10
      `);

      return {
        mostUsed: wordStats || [],
        hardest: hardestWords || [],
        easiest: easiestWords || [],
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting word analytics:', error);
      return {
        mostUsed: [],
        hardest: [],
        easiest: [],
        error: 'Failed to retrieve word analytics',
        generatedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Generate comprehensive analytics report
   */
  async getFullAnalytics() {
    try {
      const [performance, gameAnalytics, timeAnalytics, wordAnalytics] = await Promise.all([
        this.getPerformanceMetrics(),
        this.getGameAnalytics(),
        this.getTimeAnalytics(),
        this.getWordAnalytics()
      ]);

      return {
        performance,
        games: gameAnalytics,
        time: timeAnalytics,
        words: wordAnalytics,
        summary: {
          totalGames: gameAnalytics.overview.total_games || 0,
          totalGuesses: gameAnalytics.guesses.total_guesses || 0,
          serverUptime: performance.uptime,
          databaseConnected: this.db.isInitialized,
          lastUpdated: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error generating full analytics:', error);
      return {
        error: 'Failed to generate analytics report',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Middleware to track API performance
   */
  trackingMiddleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        this.trackRequest(req.path, responseTime, res.statusCode);
      });
      
      next();
    };
  }
}

module.exports = AnalyticsService;
