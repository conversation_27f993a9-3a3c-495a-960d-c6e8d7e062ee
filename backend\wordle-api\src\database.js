/**
 * Database Service - SQLite Integration
 * Handles game data persistence and statistics tracking
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class DatabaseService {
  constructor() {
    this.db = null;
    this.dbPath = path.join(__dirname, '../data/wordle.db');
    this.isInitialized = false;
  }

  /**
   * Initialize database connection and create tables
   */
  async initialize() {
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // Create database connection
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err.message);
          throw err;
        }
        console.log('Connected to SQLite database');
      });

      // Create tables
      await this.createTables();
      this.isInitialized = true;
      
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Create database tables
   */
  async createTables() {
    return new Promise((resolve, reject) => {
      const createTablesSQL = `
        -- Games table
        CREATE TABLE IF NOT EXISTS games (
          id TEXT PRIMARY KEY,
          target_word TEXT NOT NULL,
          difficulty TEXT NOT NULL DEFAULT 'medium',
          status TEXT NOT NULL DEFAULT 'in_progress',
          current_guess INTEGER NOT NULL DEFAULT 0,
          max_guesses INTEGER NOT NULL DEFAULT 6,
          start_time DATETIME NOT NULL,
          end_time DATETIME,
          duration INTEGER,
          is_daily BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- Guesses table
        CREATE TABLE IF NOT EXISTS guesses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          game_id TEXT NOT NULL,
          guess_word TEXT NOT NULL,
          guess_number INTEGER NOT NULL,
          result TEXT NOT NULL, -- JSON string of letter results
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (game_id) REFERENCES games (id)
        );

        -- Statistics table
        CREATE TABLE IF NOT EXISTS statistics (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          total_games INTEGER DEFAULT 0,
          games_won INTEGER DEFAULT 0,
          games_lost INTEGER DEFAULT 0,
          current_streak INTEGER DEFAULT 0,
          max_streak INTEGER DEFAULT 0,
          guess_distribution TEXT DEFAULT '{}', -- JSON string
          total_time INTEGER DEFAULT 0,
          last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- Words table (for word validation and daily words)
        CREATE TABLE IF NOT EXISTS words (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          word TEXT UNIQUE NOT NULL,
          difficulty TEXT NOT NULL DEFAULT 'medium',
          frequency INTEGER DEFAULT 0,
          is_common BOOLEAN DEFAULT 1,
          added_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- Initialize statistics if empty
        INSERT OR IGNORE INTO statistics (id, total_games) VALUES (1, 0);
      `;

      this.db.exec(createTablesSQL, (err) => {
        if (err) {
          console.error('Error creating tables:', err);
          reject(err);
        } else {
          console.log('Database tables created successfully');
          resolve();
        }
      });
    });
  }

  /**
   * Save a new game to database
   */
  async saveGame(game) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO games (id, target_word, difficulty, status, current_guess, max_guesses, start_time, is_daily)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const params = [
        game.id,
        game.targetWord,
        game.difficulty,
        game.status,
        game.currentGuess,
        game.maxGuesses,
        game.startTime.toISOString(),
        game.daily || false
      ];

      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('Error saving game:', err);
          reject(err);
        } else {
          console.log(`Game saved with ID: ${game.id}`);
          resolve(this.lastID);
        }
      });
    });
  }

  /**
   * Update game status
   */
  async updateGame(gameId, updates) {
    return new Promise((resolve, reject) => {
      const fields = [];
      const params = [];

      if (updates.status) {
        fields.push('status = ?');
        params.push(updates.status);
      }
      if (updates.currentGuess !== undefined) {
        fields.push('current_guess = ?');
        params.push(updates.currentGuess);
      }
      if (updates.endTime) {
        fields.push('end_time = ?');
        params.push(updates.endTime.toISOString());
      }
      if (updates.duration !== undefined) {
        fields.push('duration = ?');
        params.push(updates.duration);
      }

      if (fields.length === 0) {
        resolve();
        return;
      }

      params.push(gameId);
      const sql = `UPDATE games SET ${fields.join(', ')} WHERE id = ?`;

      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('Error updating game:', err);
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Save a guess to database
   */
  async saveGuess(gameId, guess, guessNumber, result) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO guesses (game_id, guess_word, guess_number, result)
        VALUES (?, ?, ?, ?)
      `;
      
      const params = [
        gameId,
        guess,
        guessNumber,
        JSON.stringify(result)
      ];

      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('Error saving guess:', err);
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  /**
   * Get game by ID
   */
  async getGame(gameId) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM games WHERE id = ?';
      
      this.db.get(sql, [gameId], (err, row) => {
        if (err) {
          console.error('Error getting game:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  /**
   * Get guesses for a game
   */
  async getGuesses(gameId) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM guesses WHERE game_id = ? ORDER BY guess_number';
      
      this.db.all(sql, [gameId], (err, rows) => {
        if (err) {
          console.error('Error getting guesses:', err);
          reject(err);
        } else {
          // Parse result JSON
          const guesses = rows.map(row => ({
            ...row,
            result: JSON.parse(row.result)
          }));
          resolve(guesses);
        }
      });
    });
  }

  /**
   * Update statistics
   */
  async updateStatistics(stats) {
    return new Promise((resolve, reject) => {
      const sql = `
        UPDATE statistics SET 
          total_games = ?,
          games_won = ?,
          games_lost = ?,
          current_streak = ?,
          max_streak = ?,
          guess_distribution = ?,
          total_time = ?,
          last_updated = CURRENT_TIMESTAMP
        WHERE id = 1
      `;
      
      const params = [
        stats.totalGames,
        stats.gamesWon,
        stats.gamesLost,
        stats.currentStreak,
        stats.maxStreak,
        JSON.stringify(stats.guessDistribution),
        stats.totalTime
      ];

      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('Error updating statistics:', err);
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Get statistics
   */
  async getStatistics() {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM statistics WHERE id = 1';
      
      this.db.get(sql, [], (err, row) => {
        if (err) {
          console.error('Error getting statistics:', err);
          reject(err);
        } else if (row) {
          // Parse guess distribution JSON
          const stats = {
            ...row,
            guessDistribution: JSON.parse(row.guess_distribution || '{}')
          };
          resolve(stats);
        } else {
          // Return default stats if none exist
          resolve({
            total_games: 0,
            games_won: 0,
            games_lost: 0,
            current_streak: 0,
            max_streak: 0,
            guessDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0 },
            total_time: 0
          });
        }
      });
    });
  }

  /**
   * Add words to database
   */
  async addWords(words) {
    return new Promise((resolve, reject) => {
      const sql = 'INSERT OR IGNORE INTO words (word, difficulty) VALUES (?, ?)';
      const stmt = this.db.prepare(sql);
      
      let completed = 0;
      let errors = [];

      words.forEach(({ word, difficulty = 'medium' }) => {
        stmt.run([word.toLowerCase(), difficulty], function(err) {
          completed++;
          if (err) {
            errors.push({ word, error: err.message });
          }
          
          if (completed === words.length) {
            stmt.finalize();
            if (errors.length > 0) {
              console.warn('Some words failed to insert:', errors);
            }
            resolve({ inserted: completed - errors.length, errors });
          }
        });
      });
    });
  }

  /**
   * Check if word exists in database
   */
  async isValidWord(word) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT COUNT(*) as count FROM words WHERE word = ?';
      
      this.db.get(sql, [word.toLowerCase()], (err, row) => {
        if (err) {
          console.error('Error checking word:', err);
          reject(err);
        } else {
          resolve(row.count > 0);
        }
      });
    });
  }

  /**
   * Get random word by difficulty
   */
  async getRandomWord(difficulty = 'medium') {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT word FROM words WHERE difficulty = ? ORDER BY RANDOM() LIMIT 1';
      
      this.db.get(sql, [difficulty], (err, row) => {
        if (err) {
          console.error('Error getting random word:', err);
          reject(err);
        } else {
          resolve(row ? row.word : null);
        }
      });
    });
  }

  /**
   * Close database connection
   */
  async close() {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
            reject(err);
          } else {
            console.log('Database connection closed');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }
}

module.exports = DatabaseService;
