/**
 * Wordle Game API
 * A complete word guessing game with validation and statistics
 *
 * Features:
 * - Word validation with color-coded feedback
 * - Game state management
 * - Basic statistics tracking
 * - Daily word challenges
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { WordleGame, GameStatistics, WordValidator, DIFFICULTY_LEVELS } = require('./types');
const DatabaseService = require('./database');
const AnalyticsService = require('./analytics');

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize services
const wordValidator = new WordValidator();
const gameStatistics = new GameStatistics();
const databaseService = new DatabaseService();
const analyticsService = new AnalyticsService(databaseService);
const activeGames = new Map(); // In-memory cache for active games

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());
app.use(analyticsService.trackingMiddleware()); // Track API performance

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Add more words to our validator
const ADDITIONAL_WORDS = [
  'about',
  'above',
  'abuse',
  'actor',
  'acute',
  'admit',
  'adopt',
  'adult',
  'after',
  'again',
  'agent',
  'agree',
  'ahead',
  'alarm',
  'album',
  'alert',
  'alien',
  'align',
  'alike',
  'alive',
  'allow',
  'alone',
  'along',
  'alter',
  'among',
  'anger',
  'angle',
  'angry',
  'apart',
  'apple',
  'apply',
  'arena',
  'argue',
  'arise',
  'array',
  'aside',
  'asset',
  'audio',
  'audit',
  'avoid',
  'awake',
  'award',
  'aware',
  'badly',
  'basic',
  'beach',
  'began',
  'begin',
  'being',
  'below',
  'bench',
  'billy',
  'birth',
  'black',
  'blame',
  'blind',
  'block',
  'blood',
  'board',
  'boost',
  'booth',
  'bound',
  'brain',
  'brand',
  'brave',
  'bread',
  'break',
  'breed',
  'brief',
  'bring',
  'broad',
  'broke',
  'brown',
  'build',
  'built',
  'buyer',
  'cable',
  'calif',
  'carry',
  'catch',
  'cause',
  'chain',
  'chair',
  'chaos',
  'charm',
  'chart',
  'chase',
  'cheap',
  'check',
  'chest',
  'chief',
  'child',
  'china',
  'chose',
  'civil',
  'claim',
  'class',
  'clean',
  'clear',
  'click',
  'climb',
  'clock',
  'close',
  'cloud',
  'coach',
  'coast',
  'could',
  'count',
  'court',
  'cover',
  'craft',
  'crash',
  'crazy',
  'cream',
  'crime',
  'cross',
  'crowd',
  'crown',
  'crude',
  'curve',
  'cycle',
  'daily',
  'dance',
  'dated',
  'dealt',
  'death',
  'debut',
  'delay',
  'depth',
  'doing',
  'doubt',
  'dozen',
  'draft',
  'drama',
  'drank',
  'dream',
  'dress',
  'drill',
  'drink',
  'drive',
  'drove',
  'dying',
  'eager',
  'early',
  'earth',
  'eight',
  'elite',
  'empty',
  'enemy',
  'enjoy',
  'enter',
  'entry',
  'equal',
  'error',
  'event',
  'every',
  'exact',
  'exist',
  'extra',
  'faith',
  'false',
  'fault',
  'fiber',
  'field',
  'fifth',
  'fifty',
  'fight',
  'final',
  'first',
  'fixed',
  'flash',
  'fleet',
  'floor',
  'fluid',
  'focus',
  'force',
  'forth',
  'forty',
  'forum',
  'found',
  'frame',
  'frank',
  'fraud',
  'fresh',
  'front',
  'fruit',
  'fully',
  'funny',
  'giant',
  'given',
  'glass',
  'globe',
  'going',
  'grace',
  'grade',
  'grand',
  'grant',
  'grass',
  'grave',
  'great',
  'green',
  'gross',
  'group',
  'grown',
  'guard',
  'guess',
  'guest',
  'guide',
  'happy',
  'harry',
  'heart',
  'heavy',
  'hence',
  'henry',
  'horse',
  'hotel',
  'house',
  'human',
  'ideal',
  'image',
  'index',
  'inner',
  'input',
  'issue',
  'japan',
  'jimmy',
  'joint',
  'jones',
  'judge',
  'known',
  'label',
  'large',
  'laser',
  'later',
  'laugh',
  'layer',
  'learn',
  'lease',
  'least',
  'leave',
  'legal',
  'level',
  'lewis',
  'light',
  'limit',
  'links',
  'lives',
  'local',
  'loose',
  'lower',
  'lucky',
  'lunch',
  'lying',
  'magic',
  'major',
  'maker',
  'march',
  'maria',
  'match',
  'maybe',
  'mayor',
  'meant',
  'media',
  'metal',
  'might',
  'minor',
  'minus',
  'mixed',
  'model',
  'money',
  'month',
  'moral',
  'motor',
  'mount',
  'mouse',
  'mouth',
  'moved',
  'movie',
  'music',
  'needs',
  'never',
  'newly',
  'night',
  'noise',
  'north',
  'noted',
  'novel',
  'nurse',
  'occur',
  'ocean',
  'offer',
  'often',
  'order',
  'other',
  'ought',
  'paint',
  'panel',
  'paper',
  'party',
  'peace',
  'peter',
  'phase',
  'phone',
  'photo',
  'piano',
  'piece',
  'pilot',
  'pitch',
  'place',
  'plain',
  'plane',
  'plant',
  'plate',
  'point',
  'pound',
  'power',
  'press',
  'price',
  'pride',
  'prime',
  'print',
  'prior',
  'prize',
  'proof',
  'proud',
  'prove',
  'queen',
  'quick',
  'quiet',
  'quite',
  'radio',
  'raise',
  'range',
  'rapid',
  'ratio',
  'reach',
  'ready',
  'realm',
  'rebel',
  'refer',
  'relax',
  'repay',
  'reply',
  'right',
  'rigid',
  'rival',
  'river',
  'robin',
  'roger',
  'roman',
  'rough',
  'round',
  'route',
  'royal',
  'rural',
  'scale',
  'scene',
  'scope',
  'score',
  'sense',
  'serve',
  'seven',
  'shall',
  'shape',
  'share',
  'sharp',
  'sheet',
  'shelf',
  'shell',
  'shift',
  'shine',
  'shirt',
  'shock',
  'shoot',
  'short',
  'shown',
  'sight',
  'silly',
  'since',
  'sixth',
  'sixty',
  'sized',
  'skill',
  'sleep',
  'slide',
  'small',
  'smart',
  'smile',
  'smith',
  'smoke',
  'snake',
  'snow',
  'solid',
  'solve',
  'sorry',
  'sound',
  'south',
  'space',
  'spare',
  'speak',
  'speed',
  'spend',
  'spent',
  'split',
  'spoke',
  'sport',
  'staff',
  'stage',
  'stake',
  'stand',
  'start',
  'state',
  'steam',
  'steel',
  'steep',
  'steer',
  'stick',
  'still',
  'stock',
  'stone',
  'stood',
  'store',
  'storm',
  'story',
  'strip',
  'stuck',
  'study',
  'stuff',
  'style',
  'sugar',
  'suite',
  'super',
  'sweet',
  'table',
  'taken',
  'taste',
  'taxes',
  'teach',
  'teeth',
  'terry',
  'texas',
  'thank',
  'theft',
  'their',
  'theme',
  'there',
  'these',
  'thick',
  'thing',
  'think',
  'third',
  'those',
  'three',
  'threw',
  'throw',
  'thumb',
  'tiger',
  'tight',
  'timer',
  'tired',
  'title',
  'today',
  'topic',
  'total',
  'touch',
  'tough',
  'tower',
  'track',
  'trade',
  'train',
  'treat',
  'trend',
  'trial',
  'tribe',
  'trick',
  'tried',
  'tries',
  'truck',
  'truly',
  'trunk',
  'trust',
  'truth',
  'twice',
  'uncle',
  'under',
  'undue',
  'union',
  'unity',
  'until',
  'upper',
  'upset',
  'urban',
  'usage',
  'usual',
  'valid',
  'value',
  'video',
  'virus',
  'visit',
  'vital',
  'vocal',
  'voice',
  'waste',
  'watch',
  'water',
  'wheel',
  'where',
  'which',
  'while',
  'white',
  'whole',
  'whose',
  'woman',
  'women',
  'world',
  'worry',
  'worse',
  'worst',
  'worth',
  'would',
  'write',
  'wrong',
  'wrote',
  'young',
  'youth'
];

// Initialize word validator with additional words
wordValidator.addWords(ADDITIONAL_WORDS);

// Initialize database and populate with words
async function initializeDatabase() {
  try {
    await databaseService.initialize();

    // Add words to database
    const wordsToAdd = ADDITIONAL_WORDS.map(word => ({
      word,
      difficulty: word.length === 4 ? 'easy' : word.length === 6 ? 'hard' : 'medium'
    }));

    await databaseService.addWords(wordsToAdd);
    console.log('Database initialized with word list');
  } catch (error) {
    console.error('Database initialization failed:', error);
    process.exit(1);
  }
}

// Initialize database on startup
initializeDatabase();

// Helper functions for daily words
function getTodaysWord(difficulty = DIFFICULTY_LEVELS.MEDIUM) {
  const today = new Date().toDateString();
  const seed = today.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

  // Use seed to get consistent daily word
  const randomWord = wordValidator.getRandomWord(difficulty);
  return randomWord;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'wordle-api',
    version: '2.0.0',
    activeGames: activeGames.size
  });
});

// Create new game
app.post('/api/v2/wordle/new', async (req, res) => {
  try {
    const { difficulty = DIFFICULTY_LEVELS.MEDIUM, daily = false } = req.body;

    // Validate difficulty
    if (!Object.values(DIFFICULTY_LEVELS).includes(difficulty)) {
      return res.status(400).json({
        error: 'Invalid difficulty. Must be easy, medium, or hard.'
      });
    }

    // Get target word from database or fallback to validator
    let targetWord;
    try {
      targetWord = await databaseService.getRandomWord(difficulty);
      if (!targetWord) {
        targetWord = daily ? getTodaysWord(difficulty) : wordValidator.getRandomWord(difficulty);
      }
    } catch (dbError) {
      console.warn('Database word fetch failed, using fallback:', dbError);
      targetWord = daily ? getTodaysWord(difficulty) : wordValidator.getRandomWord(difficulty);
    }

    const game = new WordleGame(targetWord, difficulty);
    game.daily = daily;

    // Save to database
    try {
      await databaseService.saveGame(game);
    } catch (dbError) {
      console.warn('Failed to save game to database:', dbError);
      // Continue without database - game will work in memory
    }

    activeGames.set(game.id, game);

    // Track analytics
    analyticsService.trackGameCreation(difficulty, daily);

    res.json({
      success: true,
      data: {
        gameId: game.id,
        difficulty: game.difficulty,
        maxGuesses: game.maxGuesses,
        currentGuess: game.currentGuess,
        status: game.status,
        daily: daily
      },
      message: 'New game created successfully'
    });
  } catch (error) {
    console.error('New game error:', error);
    res.status(500).json({
      error: 'Failed to create new game'
    });
  }
});

// Make a guess
app.post('/api/v2/wordle/:gameId/guess', async (req, res) => {
  try {
    const { gameId } = req.params;
    const { guess } = req.body;

    // Validate input
    if (!guess || typeof guess !== 'string') {
      return res.status(400).json({
        error: 'Guess is required and must be a string'
      });
    }

    const game = activeGames.get(gameId);
    if (!game) {
      return res.status(404).json({
        error: 'Game not found'
      });
    }

    // Validate word length based on difficulty
    const expectedLength =
      game.difficulty === DIFFICULTY_LEVELS.EASY
        ? 4
        : game.difficulty === DIFFICULTY_LEVELS.HARD
          ? 6
          : 5;

    if (guess.length !== expectedLength) {
      return res.status(400).json({
        error: `Guess must be ${expectedLength} letters for ${game.difficulty} difficulty`
      });
    }

    // Validate word exists (check database first, then fallback)
    let isValid = false;
    try {
      isValid = await databaseService.isValidWord(guess);
    } catch (dbError) {
      console.warn('Database word validation failed, using fallback:', dbError);
      isValid = wordValidator.isValidWord(guess);
    }

    if (!isValid) {
      return res.status(400).json({
        error: 'Not a valid word'
      });
    }

    // Make the guess
    const result = game.makeGuess(guess);

    // Save guess to database
    try {
      await databaseService.saveGuess(gameId, guess, game.currentGuess, result.guess);
    } catch (dbError) {
      console.warn('Failed to save guess to database:', dbError);
      // Continue without database
    }

    // Update game in database if game is over
    if (result.isGameOver) {
      try {
        await databaseService.updateGame(gameId, {
          status: result.gameStatus,
          currentGuess: game.currentGuess,
          endTime: game.endTime,
          duration: game.duration
        });
      } catch (dbError) {
        console.warn('Failed to update game in database:', dbError);
      }

      // Update statistics
      gameStatistics.updateStats(game);

      try {
        const stats = gameStatistics.getStats();
        await databaseService.updateStatistics({
          totalGames: parseInt(stats.totalGames),
          gamesWon: parseInt(stats.gamesWon),
          gamesLost: parseInt(stats.gamesLost),
          currentStreak: parseInt(stats.currentStreak),
          maxStreak: parseInt(stats.maxStreak),
          guessDistribution: stats.guessDistribution,
          totalTime: parseInt(stats.averageTime) * parseInt(stats.totalGames) // Approximate
        });
      } catch (dbError) {
        console.warn('Failed to update statistics in database:', dbError);
      }
    }

    // Track analytics
    const responseTime = Date.now() - (req.startTime || Date.now());
    analyticsService.trackGuess(gameId, guess, result, responseTime);

    res.json({
      success: true,
      data: {
        guess: result.guess,
        gameStatus: result.gameStatus,
        isGameOver: result.isGameOver,
        targetWord: result.targetWord,
        guessesRemaining: result.guessesRemaining,
        currentGuess: game.currentGuess,
        statistics: result.isGameOver ? gameStatistics.getStats() : null
      },
      message: result.isGameOver
        ? result.gameStatus === 'won'
          ? 'Congratulations! You won!'
          : 'Game over! Better luck next time!'
        : 'Guess processed successfully'
    });
  } catch (error) {
    console.error('Guess error:', error);
    res.status(400).json({
      error: error.message || 'Failed to process guess'
    });
  }
});

// Get game state
app.get('/api/v2/wordle/:gameId', (req, res) => {
  try {
    const { gameId } = req.params;
    const game = activeGames.get(gameId);

    if (!game) {
      return res.status(404).json({
        error: 'Game not found'
      });
    }

    res.json({
      success: true,
      data: game.getGameState(),
      message: 'Game state retrieved successfully'
    });
  } catch (error) {
    console.error('Get game error:', error);
    res.status(500).json({
      error: 'Failed to retrieve game state'
    });
  }
});

// Get statistics
app.get('/api/v2/wordle/stats', async (req, res) => {
  try {
    let stats;

    // Try to get stats from database first
    try {
      const dbStats = await databaseService.getStatistics();
      stats = {
        totalGames: dbStats.total_games,
        gamesWon: dbStats.games_won,
        gamesLost: dbStats.games_lost,
        winRate:
          dbStats.total_games > 0
            ? ((dbStats.games_won / dbStats.total_games) * 100).toFixed(1)
            : '0.0',
        currentStreak: dbStats.current_streak,
        maxStreak: dbStats.max_streak,
        guessDistribution: dbStats.guessDistribution,
        averageGuesses:
          dbStats.games_won > 0
            ? (
                Object.entries(dbStats.guessDistribution).reduce(
                  (sum, [guesses, count]) => sum + parseInt(guesses) * count,
                  0
                ) / dbStats.games_won
              ).toFixed(1)
            : '0.0',
        averageTime: Math.round((dbStats.total_time || 0) / Math.max(dbStats.total_games, 1)),
        lastUpdated: dbStats.last_updated
      };
    } catch (dbError) {
      console.warn('Failed to get stats from database, using in-memory:', dbError);
      stats = gameStatistics.getStats();
    }

    res.json({
      success: true,
      data: {
        ...stats,
        activeGames: activeGames.size,
        serverUptime: Math.floor(process.uptime()),
        databaseConnected: databaseService.isInitialized
      },
      message: 'Statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve statistics'
    });
  }
});

// Get daily word info (without revealing the word)
app.get('/api/v2/wordle/daily', (req, res) => {
  try {
    const { difficulty = DIFFICULTY_LEVELS.MEDIUM } = req.query;

    if (!Object.values(DIFFICULTY_LEVELS).includes(difficulty)) {
      return res.status(400).json({
        error: 'Invalid difficulty. Must be easy, medium, or hard.'
      });
    }

    const today = new Date().toDateString();
    const expectedLength =
      difficulty === DIFFICULTY_LEVELS.EASY ? 4 : difficulty === DIFFICULTY_LEVELS.HARD ? 6 : 5;

    res.json({
      success: true,
      data: {
        date: today,
        difficulty: difficulty,
        wordLength: expectedLength,
        maxGuesses: 6
      },
      message: 'Daily word info retrieved successfully'
    });
  } catch (error) {
    console.error('Daily word error:', error);
    res.status(500).json({
      error: 'Failed to retrieve daily word info'
    });
  }
});

// API info endpoint
app.get('/api/v2/wordle/info', (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'Wordle Game API',
      version: '2.0.0',
      features: [
        'Multiple difficulty levels (easy/medium/hard)',
        'Daily word challenges',
        'Real-time game statistics',
        'Word validation',
        'Game state management'
      ],
      difficulties: {
        easy: '4-letter words',
        medium: '5-letter words (classic)',
        hard: '6-letter words'
      },
      endpoints: {
        'POST /api/v2/wordle/new': 'Create new game',
        'POST /api/v2/wordle/:gameId/guess': 'Make a guess',
        'GET /api/v2/wordle/:gameId': 'Get game state',
        'GET /api/v2/wordle/stats': 'Get statistics',
        'GET /api/v2/wordle/daily': 'Get daily word info',
        'GET /api/v2/wordle/info': 'Get API information'
      }
    },
    message: 'Wordle API information'
  });
});

// Analytics endpoints
app.get('/api/v2/wordle/analytics', async (req, res) => {
  try {
    const analytics = await analyticsService.getFullAnalytics();

    res.json({
      success: true,
      data: analytics,
      message: 'Analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({
      error: 'Failed to retrieve analytics'
    });
  }
});

app.get('/api/v2/wordle/analytics/performance', async (req, res) => {
  try {
    const performance = analyticsService.getPerformanceMetrics();

    res.json({
      success: true,
      data: performance,
      message: 'Performance metrics retrieved successfully'
    });
  } catch (error) {
    console.error('Performance metrics error:', error);
    res.status(500).json({
      error: 'Failed to retrieve performance metrics'
    });
  }
});

app.get('/api/v2/wordle/analytics/games', async (req, res) => {
  try {
    const gameAnalytics = await analyticsService.getGameAnalytics();

    res.json({
      success: true,
      data: gameAnalytics,
      message: 'Game analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Game analytics error:', error);
    res.status(500).json({
      error: 'Failed to retrieve game analytics'
    });
  }
});

app.get('/api/v2/wordle/analytics/words', async (req, res) => {
  try {
    const wordAnalytics = await analyticsService.getWordAnalytics();

    res.json({
      success: true,
      data: wordAnalytics,
      message: 'Word analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Word analytics error:', error);
    res.status(500).json({
      error: 'Failed to retrieve word analytics'
    });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Wordle API server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
