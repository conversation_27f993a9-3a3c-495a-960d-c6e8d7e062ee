/**
 * Wordle API Types - Simple, Working Definitions
 * Only includes types that are actually implemented
 */

// Game state enums
const GAME_STATUS = {
  IN_PROGRESS: 'in_progress',
  WON: 'won',
  LOST: 'lost'
};

const LETTER_STATUS = {
  CORRECT: 'correct',     // Green - right letter, right position
  PRESENT: 'present',     // Yellow - right letter, wrong position
  ABSENT: 'absent'        // Gray - letter not in word
};

const DIFFICULTY_LEVELS = {
  EASY: 'easy',       // 4-letter words
  MEDIUM: 'medium',   // 5-letter words (classic)
  HARD: 'hard'        // 6-letter words
};

// Core game interfaces
class WordleGame {
  constructor(targetWord, difficulty = DIFFICULTY_LEVELS.MEDIUM) {
    this.id = this.generateGameId();
    this.targetWord = targetWord.toLowerCase();
    this.difficulty = difficulty;
    this.guesses = [];
    this.status = GAME_STATUS.IN_PROGRESS;
    this.maxGuesses = 6;
    this.startTime = new Date();
    this.endTime = null;
    this.currentGuess = 0;
  }

  generateGameId() {
    return 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  makeGuess(word) {
    if (this.status !== GAME_STATUS.IN_PROGRESS) {
      throw new Error('Game is already finished');
    }

    if (this.currentGuess >= this.maxGuesses) {
      throw new Error('Maximum guesses reached');
    }

    const guess = this.validateGuess(word.toLowerCase());
    this.guesses.push(guess);
    this.currentGuess++;

    // Check win condition
    if (guess.every(letter => letter.status === LETTER_STATUS.CORRECT)) {
      this.status = GAME_STATUS.WON;
      this.endTime = new Date();
    } else if (this.currentGuess >= this.maxGuesses) {
      this.status = GAME_STATUS.LOST;
      this.endTime = new Date();
    }

    return {
      guess,
      gameStatus: this.status,
      isGameOver: this.status !== GAME_STATUS.IN_PROGRESS,
      targetWord: this.status !== GAME_STATUS.IN_PROGRESS ? this.targetWord : null,
      guessesRemaining: this.maxGuesses - this.currentGuess
    };
  }

  validateGuess(word) {
    const target = this.targetWord.split('');
    const guess = word.split('');
    const result = [];

    // First pass: mark correct letters
    const targetCounts = {};
    for (let i = 0; i < target.length; i++) {
      if (guess[i] === target[i]) {
        result[i] = { letter: guess[i], status: LETTER_STATUS.CORRECT };
        target[i] = null; // Mark as used
      } else {
        targetCounts[target[i]] = (targetCounts[target[i]] || 0) + 1;
      }
    }

    // Second pass: mark present/absent letters
    for (let i = 0; i < guess.length; i++) {
      if (!result[i]) { // Not already marked as correct
        const letter = guess[i];
        if (targetCounts[letter] > 0) {
          result[i] = { letter, status: LETTER_STATUS.PRESENT };
          targetCounts[letter]--;
        } else {
          result[i] = { letter, status: LETTER_STATUS.ABSENT };
        }
      }
    }

    return result;
  }

  getGameState() {
    return {
      id: this.id,
      status: this.status,
      guesses: this.guesses,
      currentGuess: this.currentGuess,
      maxGuesses: this.maxGuesses,
      difficulty: this.difficulty,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.endTime ? this.endTime - this.startTime : null
    };
  }
}

// Simple statistics tracking
class GameStatistics {
  constructor() {
    this.totalGames = 0;
    this.gamesWon = 0;
    this.gamesLost = 0;
    this.currentStreak = 0;
    this.maxStreak = 0;
    this.guessDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0 };
    this.averageGuesses = 0;
    this.totalTime = 0;
    this.averageTime = 0;
  }

  updateStats(game) {
    this.totalGames++;
    
    if (game.status === GAME_STATUS.WON) {
      this.gamesWon++;
      this.currentStreak++;
      this.maxStreak = Math.max(this.maxStreak, this.currentStreak);
      this.guessDistribution[game.currentGuess]++;
    } else {
      this.gamesLost++;
      this.currentStreak = 0;
    }

    if (game.duration) {
      this.totalTime += game.duration;
      this.averageTime = this.totalTime / this.totalGames;
    }

    this.averageGuesses = this.gamesWon > 0 
      ? Object.entries(this.guessDistribution)
          .reduce((sum, [guesses, count]) => sum + (parseInt(guesses) * count), 0) / this.gamesWon
      : 0;
  }

  getStats() {
    return {
      totalGames: this.totalGames,
      gamesWon: this.gamesWon,
      gamesLost: this.gamesLost,
      winRate: this.totalGames > 0 ? (this.gamesWon / this.totalGames * 100).toFixed(1) : 0,
      currentStreak: this.currentStreak,
      maxStreak: this.maxStreak,
      guessDistribution: this.guessDistribution,
      averageGuesses: this.averageGuesses.toFixed(1),
      averageTime: Math.round(this.averageTime / 1000), // Convert to seconds
      lastUpdated: new Date().toISOString()
    };
  }
}

// Word validation
class WordValidator {
  constructor() {
    // In a real implementation, this would load from a dictionary file
    this.validWords = new Set();
    this.commonWords = [
      // 5-letter words for medium difficulty
      'about', 'other', 'which', 'their', 'would', 'there', 'could', 'still',
      'after', 'first', 'never', 'these', 'think', 'where', 'being', 'every',
      'great', 'might', 'shall', 'those', 'under', 'while', 'sound', 'house',
      'right', 'small', 'large', 'place', 'young', 'years', 'state', 'world'
    ];
    
    // Initialize with common words
    this.commonWords.forEach(word => this.validWords.add(word.toLowerCase()));
  }

  isValidWord(word) {
    return this.validWords.has(word.toLowerCase());
  }

  getRandomWord(difficulty = DIFFICULTY_LEVELS.MEDIUM) {
    const words = Array.from(this.validWords);
    const filteredWords = words.filter(word => {
      switch (difficulty) {
        case DIFFICULTY_LEVELS.EASY: return word.length === 4;
        case DIFFICULTY_LEVELS.HARD: return word.length === 6;
        default: return word.length === 5;
      }
    });
    
    if (filteredWords.length === 0) {
      // Fallback to 5-letter words
      return words.find(word => word.length === 5) || 'house';
    }
    
    return filteredWords[Math.floor(Math.random() * filteredWords.length)];
  }

  addWord(word) {
    this.validWords.add(word.toLowerCase());
  }

  addWords(words) {
    words.forEach(word => this.addWord(word));
  }
}

module.exports = {
  GAME_STATUS,
  LETTER_STATUS,
  DIFFICULTY_LEVELS,
  WordleGame,
  GameStatistics,
  WordValidator
};
