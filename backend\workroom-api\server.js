const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const moment = require('moment');
const _ = require('lodash');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

const PORT = process.env.PORT || 3006;
const JWT_SECRET =
  process.env.JWT_SECRET ||
  (process.env.NODE_ENV === 'production' ? undefined : 'dev-only-secret-key');

// Security and performance middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", 'https:'],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'ws:', 'wss:']
      }
    }
  })
);
app.use(compression());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // limit each IP to 200 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// In-memory storage (in production, use Redis/MongoDB)
const workrooms = new Map();
const users = new Map();
const activeConnections = new Map();

// Room data structure
class WorkRoom {
  constructor(name, password = null, createdBy) {
    this.id = uuidv4();
    this.name = name;
    this.password = password ? bcrypt.hashSync(password, 10) : null;
    this.createdBy = createdBy;
    this.createdAt = moment().toISOString();
    this.members = new Set();
    this.messages = [];
    this.tasks = [];
    this.whiteboard = {
      elements: [],
      lastModified: moment().toISOString()
    };
    this.focusTimer = {
      duration: 25 * 60, // 25 minutes default
      remaining: 0,
      isActive: false,
      startedBy: null,
      startedAt: null
    };
    this.settings = {
      maxMembers: 50,
      allowAnonymous: true,
      enableChat: true,
      enableTasks: true,
      enableWhiteboard: true,
      enableTimer: true
    };
  }

  addMember(userId) {
    this.members.add(userId);
  }

  removeMember(userId) {
    this.members.delete(userId);
  }

  addMessage(message) {
    const msg = {
      id: uuidv4(),
      ...message,
      timestamp: moment().toISOString()
    };
    this.messages.push(msg);

    // Keep only last 100 messages
    if (this.messages.length > 100) {
      this.messages = this.messages.slice(-100);
    }

    return msg;
  }

  addTask(task) {
    const newTask = {
      id: uuidv4(),
      ...task,
      createdAt: moment().toISOString(),
      completed: false
    };
    this.tasks.push(newTask);
    return newTask;
  }

  updateTask(taskId, updates) {
    const taskIndex = this.tasks.findIndex(t => t.id === taskId);
    if (taskIndex !== -1) {
      this.tasks[taskIndex] = { ...this.tasks[taskIndex], ...updates };
      return this.tasks[taskIndex];
    }
    return null;
  }

  deleteTask(taskId) {
    const taskIndex = this.tasks.findIndex(t => t.id === taskId);
    if (taskIndex !== -1) {
      return this.tasks.splice(taskIndex, 1)[0];
    }
    return null;
  }

  updateWhiteboard(elements) {
    this.whiteboard.elements = elements;
    this.whiteboard.lastModified = moment().toISOString();
  }

  startFocusTimer(duration, startedBy) {
    this.focusTimer = {
      duration,
      remaining: duration,
      isActive: true,
      startedBy,
      startedAt: moment().toISOString()
    };
  }

  stopFocusTimer() {
    this.focusTimer.isActive = false;
    this.focusTimer.remaining = 0;
  }

  getPublicData() {
    return {
      id: this.id,
      name: this.name,
      hasPassword: !!this.password,
      memberCount: this.members.size,
      createdAt: this.createdAt,
      settings: this.settings
    };
  }

  getFullData(userId) {
    if (!this.members.has(userId)) {
      return null;
    }

    return {
      id: this.id,
      name: this.name,
      members: Array.from(this.members),
      messages: this.messages.slice(-50), // Last 50 messages
      tasks: this.tasks,
      whiteboard: this.whiteboard,
      focusTimer: this.focusTimer,
      settings: this.settings
    };
  }
}

// User data structure
class User {
  constructor(username, isAnonymous = true) {
    this.id = uuidv4();
    this.username = username;
    this.isAnonymous = isAnonymous;
    this.joinedAt = moment().toISOString();
    this.currentRoom = null;
    this.avatar = this.generateAvatar();
    this.status = 'online';
  }

  generateAvatar() {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return {
      color: colors[Math.floor(Math.random() * colors.length)],
      initials: this.username.substring(0, 2).toUpperCase()
    };
  }
}

// Utility functions
function generateRoomCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

function validatePassword(password, hash) {
  return bcrypt.compareSync(password, hash);
}

// API Routes
app.get('/api/info', (req, res) => {
  res.json({
    name: 'WorkRoom API',
    version: '1.0.0',
    description: 'Enterprise-grade collaborative workspace with real-time communication',
    features: [
      'Real-time chat and collaboration',
      'Secure room management with passwords',
      'Shared task management',
      'Interactive whiteboard',
      'Focus timer (Pomodoro technique)',
      'User presence and status',
      'Room analytics and insights'
    ],
    endpoints: {
      'GET /api/rooms': 'List all public rooms',
      'POST /api/rooms': 'Create a new room',
      'POST /api/rooms/:id/join': 'Join a room',
      'GET /api/rooms/:id': 'Get room details',
      'WebSocket /': 'Real-time communication'
    },
    statistics: {
      totalRooms: workrooms.size,
      activeUsers: users.size,
      totalConnections: activeConnections.size
    }
  });
});

// Get all public rooms
app.get('/api/rooms', (req, res) => {
  const publicRooms = Array.from(workrooms.values())
    .map(room => room.getPublicData())
    .sort((a, b) => b.memberCount - a.memberCount);

  res.json({
    success: true,
    data: publicRooms,
    total: publicRooms.length
  });
});

// Create a new room
app.post(
  '/api/rooms',
  [
    body('name').isLength({ min: 1, max: 50 }).trim().escape(),
    body('password').optional().isLength({ min: 4, max: 20 }),
    body('username').isLength({ min: 1, max: 20 }).trim().escape()
  ],
  (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { name, password, username } = req.body;

    // Create user
    const user = new User(username);
    users.set(user.id, user);

    // Create room
    const room = new WorkRoom(name, password, user.id);
    room.addMember(user.id);
    user.currentRoom = room.id;

    workrooms.set(room.id, room);

    // Generate JWT token
    const token = jwt.sign({ userId: user.id, roomId: room.id }, JWT_SECRET, { expiresIn: '24h' });

    res.json({
      success: true,
      data: {
        room: room.getPublicData(),
        user: {
          id: user.id,
          username: user.username,
          avatar: user.avatar
        },
        token
      }
    });
  }
);

// Join a room
app.post(
  '/api/rooms/:id/join',
  [
    body('username').isLength({ min: 1, max: 20 }).trim().escape(),
    body('password').optional().isLength({ min: 4, max: 20 })
  ],
  (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { username, password } = req.body;

    const room = workrooms.get(id);
    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }

    // Check password if room is protected
    if (room.password && !validatePassword(password || '', room.password)) {
      return res.status(401).json({
        success: false,
        message: 'Invalid password'
      });
    }

    // Check room capacity
    if (room.members.size >= room.settings.maxMembers) {
      return res.status(403).json({
        success: false,
        message: 'Room is full'
      });
    }

    // Create user
    const user = new User(username);
    users.set(user.id, user);

    // Add user to room
    room.addMember(user.id);
    user.currentRoom = room.id;

    // Generate JWT token
    const token = jwt.sign({ userId: user.id, roomId: room.id }, JWT_SECRET, { expiresIn: '24h' });

    res.json({
      success: true,
      data: {
        room: room.getFullData(user.id),
        user: {
          id: user.id,
          username: user.username,
          avatar: user.avatar
        },
        token
      }
    });
  }
);

// Get room details
app.get('/api/rooms/:id', (req, res) => {
  const { id } = req.params;
  const room = workrooms.get(id);

  if (!room) {
    return res.status(404).json({
      success: false,
      message: 'Room not found'
    });
  }

  res.json({
    success: true,
    data: room.getPublicData()
  });
});

// WebSocket connection handling
io.on('connection', socket => {
  console.log(`🔌 New connection: ${socket.id}`);

  let currentUser = null;
  let currentRoom = null;

  // Authenticate user
  socket.on('authenticate', data => {
    try {
      const { token } = data;
      const decoded = jwt.verify(token, JWT_SECRET);

      currentUser = users.get(decoded.userId);
      currentRoom = workrooms.get(decoded.roomId);

      if (!currentUser || !currentRoom) {
        socket.emit('auth_error', { message: 'Invalid authentication' });
        return;
      }

      // Store connection
      activeConnections.set(socket.id, {
        userId: currentUser.id,
        roomId: currentRoom.id,
        connectedAt: moment().toISOString()
      });

      // Join socket room
      socket.join(currentRoom.id);

      // Update user status
      currentUser.status = 'online';

      // Notify room members
      socket.to(currentRoom.id).emit('user_joined', {
        user: {
          id: currentUser.id,
          username: currentUser.username,
          avatar: currentUser.avatar,
          status: currentUser.status
        }
      });

      // Send room data to user
      socket.emit('room_data', currentRoom.getFullData(currentUser.id));

      // Send current members list
      const members = Array.from(currentRoom.members)
        .map(memberId => {
          const member = users.get(memberId);
          return member
            ? {
                id: member.id,
                username: member.username,
                avatar: member.avatar,
                status: member.status
              }
            : null;
        })
        .filter(Boolean);

      socket.emit('members_list', members);

      console.log(`✅ User ${currentUser.username} authenticated in room ${currentRoom.name}`);
    } catch (error) {
      console.error('Authentication error:', error);
      socket.emit('auth_error', { message: 'Authentication failed' });
    }
  });

  // Handle chat messages
  socket.on('send_message', data => {
    if (!currentUser || !currentRoom) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const { content, type = 'text' } = data;

    if (!content || content.trim().length === 0) {
      return;
    }

    const message = currentRoom.addMessage({
      userId: currentUser.id,
      username: currentUser.username,
      avatar: currentUser.avatar,
      content: content.trim(),
      type
    });

    // Broadcast to all room members
    io.to(currentRoom.id).emit('new_message', message);
  });

  // Handle task operations
  socket.on('create_task', data => {
    if (!currentUser || !currentRoom) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const { title, description, priority = 'medium', assignedTo } = data;

    if (!title || title.trim().length === 0) {
      socket.emit('error', { message: 'Task title is required' });
      return;
    }

    const task = currentRoom.addTask({
      title: title.trim(),
      description: description?.trim() || '',
      priority,
      assignedTo,
      createdBy: currentUser.id,
      createdByName: currentUser.username
    });

    // Broadcast to all room members
    io.to(currentRoom.id).emit('task_created', task);
  });

  socket.on('update_task', data => {
    if (!currentUser || !currentRoom) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const { taskId, updates } = data;
    const updatedTask = currentRoom.updateTask(taskId, {
      ...updates,
      updatedBy: currentUser.id,
      updatedAt: moment().toISOString()
    });

    if (updatedTask) {
      io.to(currentRoom.id).emit('task_updated', updatedTask);
    }
  });

  socket.on('delete_task', data => {
    if (!currentUser || !currentRoom) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const { taskId } = data;
    const deletedTask = currentRoom.deleteTask(taskId);

    if (deletedTask) {
      io.to(currentRoom.id).emit('task_deleted', { taskId });
    }
  });

  // Handle whiteboard operations
  socket.on('whiteboard_update', data => {
    if (!currentUser || !currentRoom) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const { elements } = data;
    currentRoom.updateWhiteboard(elements);

    // Broadcast to other room members (not sender)
    socket.to(currentRoom.id).emit('whiteboard_updated', {
      elements,
      updatedBy: currentUser.username
    });
  });

  // Handle focus timer operations
  socket.on('start_focus_timer', data => {
    if (!currentUser || !currentRoom) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const { duration = 25 * 60 } = data; // Default 25 minutes
    currentRoom.startFocusTimer(duration, currentUser.id);

    // Broadcast to all room members
    io.to(currentRoom.id).emit('focus_timer_started', {
      duration,
      startedBy: currentUser.username,
      startedAt: currentRoom.focusTimer.startedAt
    });

    // Set up timer countdown
    const timerInterval = setInterval(() => {
      if (!currentRoom.focusTimer.isActive) {
        clearInterval(timerInterval);
        return;
      }

      currentRoom.focusTimer.remaining -= 1;

      if (currentRoom.focusTimer.remaining <= 0) {
        currentRoom.stopFocusTimer();
        io.to(currentRoom.id).emit('focus_timer_completed');
        clearInterval(timerInterval);
      } else {
        io.to(currentRoom.id).emit('focus_timer_tick', {
          remaining: currentRoom.focusTimer.remaining
        });
      }
    }, 1000);
  });

  socket.on('stop_focus_timer', () => {
    if (!currentUser || !currentRoom) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    currentRoom.stopFocusTimer();
    io.to(currentRoom.id).emit('focus_timer_stopped', {
      stoppedBy: currentUser.username
    });
  });

  // Handle user status updates
  socket.on('update_status', data => {
    if (!currentUser) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const { status } = data;
    if (['online', 'away', 'busy', 'offline'].includes(status)) {
      currentUser.status = status;

      if (currentRoom) {
        socket.to(currentRoom.id).emit('user_status_updated', {
          userId: currentUser.id,
          status
        });
      }
    }
  });

  // Handle typing indicators
  socket.on('typing_start', () => {
    if (!currentUser || !currentRoom) return;

    socket.to(currentRoom.id).emit('user_typing', {
      userId: currentUser.id,
      username: currentUser.username
    });
  });

  socket.on('typing_stop', () => {
    if (!currentUser || !currentRoom) return;

    socket.to(currentRoom.id).emit('user_stopped_typing', {
      userId: currentUser.id
    });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log(`🔌 Disconnection: ${socket.id}`);

    const connection = activeConnections.get(socket.id);
    if (connection && currentUser && currentRoom) {
      // Update user status
      currentUser.status = 'offline';

      // Notify room members
      socket.to(currentRoom.id).emit('user_left', {
        userId: currentUser.id,
        username: currentUser.username
      });

      // Remove from room if no other connections
      const userConnections = Array.from(activeConnections.values()).filter(
        conn => conn.userId === currentUser.id && conn.roomId === currentRoom.id
      );

      if (userConnections.length <= 1) {
        currentRoom.removeMember(currentUser.id);
        users.delete(currentUser.id);

        // Delete room if empty
        if (currentRoom.members.size === 0) {
          workrooms.delete(currentRoom.id);
          console.log(`🗑️ Deleted empty room: ${currentRoom.name}`);
        }
      }
    }

    activeConnections.delete(socket.id);
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 WorkRoom API running on port ${PORT}`);
  console.log(`📊 Demo available at http://localhost:${PORT}`);
  console.log(`🔌 WebSocket server ready for real-time collaboration`);
});

module.exports = { app, server };
