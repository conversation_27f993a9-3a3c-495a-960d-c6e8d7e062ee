/**
 * Jest Configuration for Portfolio Testing
 * Demonstrates testing setup and best practices
 */

export default {
  // Test environment
  testEnvironment: 'jsdom',

  // Test file patterns
  testMatch: ['**/tests/**/*.test.js', '**/projects/**/*.test.js', '**/backend/**/*.test.js'],

  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],

  // Coverage thresholds (realistic for portfolio)
  coverageThreshold: {
    global: {
      branches: 30,
      functions: 30,
      lines: 30,
      statements: 30
    }
  },

  // Files to collect coverage from
  collectCoverageFrom: [
    'main.js',
    'projects/**/main.js',
    'projects/**/*.js',
    'backend/**/src/**/*.js',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/dist/**'
  ],

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // Module name mapping for ES modules
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1'
  },

  // Transform configuration for modern JavaScript
  transform: {
    '^.+\\.js$': 'babel-jest'
  },

  // Test timeout
  testTimeout: 10000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true
};
