/**
 * Wordle Game - Comprehensive Styling
 * Modern, responsive design with animations and accessibility
 */

/* CSS Variables for theming */
:root {
  /* Light theme colors */
  --bg-color: #ffffff;
  --text-color: #1a1a1b;
  --border-color: #d3d6da;
  --key-bg: #d3d6da;
  --key-text: #1a1a1b;
  --key-bg-hover: #c3c7cb;

  /* Game colors */
  --correct: #6aaa64;
  --present: #c9b458;
  --absent: #787c7e;
  --empty: #ffffff;

  /* High contrast mode */
  --correct-contrast: #f5793a;
  --present-contrast: #85c0f9;

  /* UI elements */
  --modal-bg: rgba(255, 255, 255, 0.95);
  --modal-shadow: rgba(0, 0, 0, 0.3);
  --toast-bg: #1a1a1b;
  --toast-text: #ffffff;

  /* Spacing */
  --gap-xs: 4px;
  --gap-sm: 8px;
  --gap-md: 12px;
  --gap-lg: 16px;
  --gap-xl: 24px;

  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Dark theme */
[data-theme='dark'] {
  --bg-color: #121213;
  --text-color: #ffffff;
  --border-color: #3a3a3c;
  --key-bg: #818384;
  --key-text: #ffffff;
  --key-bg-hover: #565758;
  --empty: #121213;
  --modal-bg: rgba(18, 18, 19, 0.95);
  --toast-bg: #ffffff;
  --toast-text: #1a1a1b;
}

/* High contrast mode */
[data-contrast='high'] {
  --correct: var(--correct-contrast);
  --present: var(--present-contrast);
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.4;
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal);
}

.container {
  max-width: 500px;
  margin: 0 auto;
  padding: var(--gap-lg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--gap-lg);
  margin-bottom: var(--gap-md);
}

/* API Status Styles */
.api-status {
  padding: var(--gap-md) 0;
  margin-bottom: var(--gap-md);
  text-align: center;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-size: 0.9rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #fcc;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.game-message {
  background: #efe;
  color: #363;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #cfc;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.connection-status {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.test-api-btn,
.new-game-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-api-btn:hover,
.new-game-btn:hover {
  background: var(--primary-dark);
}

.test-api-btn:disabled,
.new-game-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--gap-lg);
  flex: 1;
}

.back-button {
  color: var(--text-color);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  padding: var(--gap-sm) var(--gap-md);
  border-radius: var(--radius-sm);
  background: var(--key-bg);
  transition: var(--transition-fast);
  white-space: nowrap;
}

.back-button:hover {
  background: var(--key-bg-hover);
  transform: translateY(-1px);
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 0.2em;
  text-align: center;
  flex: 1;
}

.header-buttons {
  display: flex;
  gap: var(--gap-sm);
}

.icon-btn {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: var(--gap-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background-color: var(--key-bg-hover);
}

.icon-btn:focus {
  outline: 2px solid var(--present);
  outline-offset: 2px;
}

/* Game Container */
.game-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--gap-xl);
  margin-top: var(--gap-xl);
}

/* Game Board */
.board {
  display: grid;
  grid-template-rows: repeat(6, 1fr);
  gap: var(--gap-xs);
  margin-bottom: var(--gap-lg);
}

.row {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--gap-xs);
}

.tile {
  width: 62px;
  height: 62px;
  border: 2px solid var(--border-color);
  background-color: var(--empty);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  text-transform: uppercase;
  transition: all var(--transition-normal);
  user-select: none;
}

.tile.filled {
  border-color: var(--absent);
  color: var(--text-color);
  animation: pop 0.1s ease-in-out;
}

.tile.correct {
  background-color: var(--correct);
  border-color: var(--correct);
  color: white;
}

.tile.present {
  background-color: var(--present);
  border-color: var(--present);
  color: white;
}

.tile.absent {
  background-color: var(--absent);
  border-color: var(--absent);
  color: white;
}

.tile.flip {
  animation: flip 0.6s ease-in-out forwards;
}

.tile.shake {
  animation: shake 0.5s ease-in-out;
}

.tile.bounce {
  animation: bounce 0.5s ease-in-out;
}

/* Game Message */
.message {
  min-height: 24px;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
  color: var(--text-color);
}

/* Virtual Keyboard */
.keyboard {
  width: 100%;
  max-width: 484px;
}

.keyboard-row {
  display: flex;
  justify-content: center;
  gap: var(--gap-xs);
  margin-bottom: var(--gap-sm);
}

.keyboard-row:last-child {
  margin-bottom: 0;
}

.key {
  background-color: var(--key-bg);
  color: var(--key-text);
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 43px;
  height: 58px;
  padding: 0 var(--gap-sm);
  user-select: none;
}

.key:hover {
  background-color: var(--key-bg-hover);
}

.key:active {
  transform: scale(0.95);
}

.key:focus {
  outline: 2px solid var(--present);
  outline-offset: 2px;
}

.key-large {
  min-width: 65px;
  font-size: 0.75rem;
}

.key.correct {
  background-color: var(--correct);
  color: white;
}

.key.present {
  background-color: var(--present);
  color: white;
}

.key.absent {
  background-color: var(--absent);
  color: white;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-shadow);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--gap-lg);
}

.modal.show {
  display: flex;
  animation: fadeIn var(--transition-normal);
}

.modal-content {
  background-color: var(--modal-bg);
  border-radius: var(--radius-lg);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px var(--modal-shadow);
  animation: slideUp var(--transition-normal);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gap-xl);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-color);
  cursor: pointer;
  padding: var(--gap-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.close-btn:hover {
  background-color: var(--key-bg-hover);
}

.modal-body {
  padding: var(--gap-xl);
}

.modal-body p {
  margin-bottom: var(--gap-md);
}

.modal-body ul {
  margin: var(--gap-md) 0;
  padding-left: var(--gap-xl);
}

.modal-body li {
  margin-bottom: var(--gap-sm);
}

/* Help Modal Examples */
.examples {
  margin: var(--gap-xl) 0;
}

.example-row {
  display: flex;
  gap: var(--gap-xs);
  margin: var(--gap-md) 0;
}

.example-row .tile {
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
}

/* Statistics Modal */
.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--gap-lg);
  margin-bottom: var(--gap-xl);
  text-align: center;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color);
  opacity: 0.8;
}

.guess-distribution {
  margin-bottom: var(--gap-xl);
}

.guess-distribution h3 {
  margin-bottom: var(--gap-md);
  font-size: 1.1rem;
}

.distribution-container {
  display: flex;
  flex-direction: column;
  gap: var(--gap-xs);
}

.distribution-bar {
  display: flex;
  align-items: center;
  gap: var(--gap-sm);
}

.distribution-number {
  font-weight: 600;
  min-width: 20px;
}

.distribution-fill {
  background-color: var(--absent);
  color: white;
  padding: var(--gap-xs) var(--gap-sm);
  border-radius: var(--radius-sm);
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  transition: background-color var(--transition-normal);
}

.distribution-fill.highlight {
  background-color: var(--correct);
}

.next-wordle {
  text-align: center;
  margin-bottom: var(--gap-xl);
}

.next-wordle h3 {
  margin-bottom: var(--gap-md);
  font-size: 1.1rem;
}

.countdown {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.share-btn {
  background-color: var(--correct);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--gap-md) var(--gap-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  width: 100%;
}

.share-btn:hover {
  background-color: #5a9a54;
}

.share-btn:disabled {
  background-color: var(--absent);
  cursor: not-allowed;
}

/* Settings Modal */
.setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gap-lg) 0;
  border-bottom: 1px solid var(--border-color);
}

.setting:last-child {
  border-bottom: none;
}

.setting-text {
  flex: 1;
}

.setting-title {
  font-weight: 600;
  margin-bottom: var(--gap-xs);
}

.setting-description {
  font-size: 0.875rem;
  opacity: 0.8;
}

.setting-switch {
  position: relative;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-label {
  display: block;
  width: 50px;
  height: 26px;
  background-color: var(--border-color);
  border-radius: 13px;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  position: relative;
}

.switch-label::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background-color: white;
  border-radius: 50%;
  transition: transform var(--transition-fast);
}

.switch-input:checked + .switch-label {
  background-color: var(--correct);
}

.switch-input:checked + .switch-label::after {
  transform: translateX(24px);
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--toast-bg);
  color: var(--toast-text);
  padding: var(--gap-md) var(--gap-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  z-index: 2000;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-normal);
}

.toast.show {
  opacity: 1;
  animation: toastSlide var(--transition-normal);
}

/* Animations */
@keyframes pop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes flip {
  0% {
    transform: rotateX(0);
  }
  50% {
    transform: rotateX(-90deg);
  }
  100% {
    transform: rotateX(0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toastSlide {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .container {
    padding: var(--gap-md);
  }

  .title {
    font-size: 2rem;
  }

  .tile {
    width: 56px;
    height: 56px;
    font-size: 1.8rem;
  }

  .key {
    min-width: 38px;
    height: 52px;
    font-size: 0.8rem;
  }

  .key-large {
    min-width: 58px;
    font-size: 0.7rem;
  }

  .stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--gap-md);
  }

  .modal-content {
    margin: var(--gap-md);
  }

  .modal-header,
  .modal-body {
    padding: var(--gap-lg);
  }
}

@media (max-width: 360px) {
  .tile {
    width: 50px;
    height: 50px;
    font-size: 1.6rem;
  }

  .key {
    min-width: 34px;
    height: 48px;
  }

  .key-large {
    min-width: 52px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.tile:focus,
.key:focus,
.icon-btn:focus,
.close-btn:focus,
.share-btn:focus {
  outline: 2px solid var(--present);
  outline-offset: 2px;
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --key-bg: #000000;
    --key-text: #ffffff;
  }

  [data-theme='dark'] {
    --border-color: #ffffff;
    --key-bg: #ffffff;
    --key-text: #000000;
  }
}
