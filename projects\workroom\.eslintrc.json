{"env": {"browser": true, "es2022": true, "node": true, "jest": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "error", "no-unused-vars": "warn"}, "ignorePatterns": ["dist", "build", "coverage", "node_modules", "*.config.js", "*.config.ts"]}