/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors - Light Theme */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-secondary: #64748b;
  --color-accent: #f59e0b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.1);

  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #2563eb;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-family-sans:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono:
    'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Dark Theme */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-overlay: rgba(255, 255, 255, 0.1);

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-inverse: #0f172a;

    --border-primary: #334155;
    --border-secondary: #475569;
  }
}

[data-theme='dark'] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --bg-overlay: rgba(255, 255, 255, 0.1);

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-inverse: #0f172a;

  --border-primary: #334155;
  --border-secondary: #475569;
}

[data-theme='light'] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.1);

  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;

  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: var(--leading-tight);
  color: var(--text-primary);
}

h1 {
  font-size: var(--text-4xl);
}
h2 {
  font-size: var(--text-3xl);
}
h3 {
  font-size: var(--text-2xl);
}
h4 {
  font-size: var(--text-xl);
}
h5 {
  font-size: var(--text-lg);
}
h6 {
  font-size: var(--text-base);
}

p {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-hover);
}

a:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

/* ===== BUTTON COMPONENTS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn-primary {
  color: var(--text-inverse);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  color: var(--text-inverse);
}

.btn-secondary {
  color: var(--text-primary);
  background-color: transparent;
  border-color: var(--border-primary);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* ===== HEADER ===== */
.header {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
  height: 64px;
}

.logo {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
}

.logo:hover {
  color: var(--text-primary);
}

.accent {
  color: var(--color-accent);
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.theme-toggle,
.github-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--bg-card);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.theme-toggle:hover,
.github-link:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
}

.theme-icon,
.github-icon {
  font-size: var(--text-lg);
}

/* ===== HERO SECTION ===== */
.hero {
  padding: var(--space-20) 0 var(--space-16);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  text-align: center;
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.hero-title {
  font-size: var(--text-5xl);
  font-weight: 800;
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--color-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 0.8s ease-out;
}

.hero-subtitle {
  display: block;
  font-size: var(--text-3xl);
  color: var(--color-accent);
  margin-top: var(--space-2);
}

.hero-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--text-4xl);
  font-weight: 800;
  color: var(--color-primary);
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--space-1);
}

@media (max-width: 768px) {
  .hero {
    padding: var(--space-16) 0 var(--space-12);
  }

  .hero-title {
    font-size: var(--text-3xl);
  }

  .hero-subtitle {
    font-size: var(--text-2xl);
  }

  .hero-description {
    font-size: var(--text-base);
  }

  .hero-stats {
    gap: var(--space-6);
  }

  .stat-number {
    font-size: var(--text-3xl);
  }
}

/* ===== PROJECTS SECTION ===== */
.projects {
  padding: var(--space-20) 0;
  background-color: var(--bg-secondary);
}

.projects-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.section-title {
  text-align: center;
  font-size: var(--text-4xl);
  font-weight: 800;
  margin-bottom: var(--space-16);
  color: var(--text-primary);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

/* GitHub Projects Section */
.github-projects-section {
  margin: 3rem 0;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.github-projects-section .section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.github-projects-section .section-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.github-projects-section .section-description {
  color: var(--text-secondary);
  font-size: 1rem;
}

.github-projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.github-project-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.github-project-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.github-project-card .project-header {
  margin-bottom: 1rem;
}

.github-project-card .project-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.github-project-card .project-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.github-project-card .project-links {
  display: flex;
  gap: 0.75rem;
}

.github-project-card .btn {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  flex: 1;
  text-align: center;
}

.project-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--border-secondary);
}

.project-card:hover::before {
  transform: scaleX(1);
}

.project-card.backend::before {
  background: linear-gradient(90deg, var(--color-success), var(--color-primary));
}

.project-card.featured {
  border: 2px solid var(--color-primary);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
}

.project-card.featured::before {
  height: 6px;
  background: linear-gradient(
    90deg,
    var(--color-primary),
    var(--color-accent),
    var(--color-success)
  );
  transform: scaleX(1);
}

/* Section Dividers */
.section-divider {
  grid-column: 1 / -1;
  text-align: center;
  margin: var(--space-8) 0 var(--space-6) 0;
  padding: var(--space-4) 0;
  border-bottom: 2px solid var(--border-primary);
  position: relative;
}

.section-divider::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
}

.section-subtitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  max-width: 400px;
  margin: 0 auto;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.project-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-left: var(--space-4);
}

.tag {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--color-primary);
  background-color: rgba(37, 99, 235, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.2);
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.backend .tag {
  color: var(--color-success);
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

.project-description {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-4);
}

.project-tech {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  flex-wrap: wrap;
}

.tech {
  font-size: var(--text-xs);
  padding: var(--space-1) var(--space-2);
  background: var(--accent-primary);
  border-radius: var(--radius-full);
  color: white;
  font-weight: 500;
  border: 1px solid var(--border-primary);
}

.project-links {
  display: flex;
  gap: var(--space-3);
  margin-top: auto;
}

.project-links .btn {
  flex: 1;
  justify-content: center;
}

/* ===== PERFORMANCE SECTION ===== */
.performance-section {
  margin-top: var(--space-16);
  padding-top: var(--space-8);
  border-top: 1px solid var(--border-primary);
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.performance-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-align: center;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.performance-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.performance-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
}

.performance-metric {
  font-size: var(--text-3xl);
  font-weight: bold;
  color: var(--color-primary);
  margin-bottom: var(--space-2);
}

.performance-label {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.performance-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .project-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .project-tags {
    margin-left: 0;
    margin-top: var(--space-2);
  }

  .project-links {
    flex-direction: column;
  }

  .project-links .btn {
    flex: none;
  }

  /* GitHub Projects Mobile Styles */
  .github-projects-section {
    margin: 2rem 0;
    padding: 1.5rem;
  }

  .github-projects-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .github-project-card {
    padding: 1.25rem;
  }

  .github-project-card .project-links {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--space-20) 0;
  background-color: var(--bg-primary);
  text-align: center;
}

.contact-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.contact-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
}

.contact-links {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.contact-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  min-width: 100px;
}

.contact-link:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-2px);
  color: var(--text-primary);
}

.contact-icon {
  font-size: var(--text-2xl);
}

@media (max-width: 768px) {
  .contact-links {
    gap: var(--space-4);
  }

  .contact-link {
    min-width: 80px;
    padding: var(--space-3);
  }
}

/* ===== CONTACT MODAL ===== */
.contact-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
}

.contact-modal-content {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.contact-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.contact-modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--text-xl);
}

.contact-modal-close {
  background: none;
  border: none;
  font-size: var(--text-2xl);
  cursor: pointer;
  color: var(--text-secondary);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.contact-modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.contact-modal-body {
  padding: var(--space-6);
}

.contact-modal-body p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contact-method {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  flex-wrap: wrap;
}

.contact-method strong {
  color: var(--text-primary);
  min-width: 120px;
}

.copy-btn {
  background: var(--color-primary);
  color: var(--text-inverse);
  border: none;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: var(--text-sm);
  transition: var(--transition-fast);
}

.copy-btn:hover {
  background: var(--color-primary-hover);
}

.copy-btn:active {
  transform: scale(0.95);
}

/* ===== NOTIFICATIONS ===== */
.notification {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-tooltip);
  max-width: 400px;
  min-width: 300px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.notification-error {
  background: rgba(239, 68, 68, 0.95);
  border: 1px solid #dc2626;
  color: white;
}

.notification-warning {
  background: rgba(245, 158, 11, 0.95);
  border: 1px solid #d97706;
  color: white;
}

.notification-success {
  background: rgba(16, 185, 129, 0.95);
  border: 1px solid #059669;
  color: white;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.notification-icon {
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: var(--text-sm);
  line-height: var(--leading-snug);
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: var(--text-xl);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  flex-shrink: 0;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== FOOTER ===== */
.footer {
  padding: var(--space-8) 0;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.footer-text {
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.footer-copyright {
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation delays for staggered effects */
.project-card:nth-child(1) {
  animation-delay: 0.1s;
}
.project-card:nth-child(2) {
  animation-delay: 0.2s;
}
.project-card:nth-child(3) {
  animation-delay: 0.3s;
}
.project-card:nth-child(4) {
  animation-delay: 0.4s;
}
.project-card:nth-child(5) {
  animation-delay: 0.5s;
}
.project-card:nth-child(6) {
  animation-delay: 0.6s;
}
.project-card:nth-child(7) {
  animation-delay: 0.7s;
}
.project-card:nth-child(8) {
  animation-delay: 0.8s;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for better accessibility */
.btn:focus-visible,
.theme-toggle:focus-visible,
.github-link:focus-visible,
.contact-link:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
  }
}

/* Print styles */
@media print {
  .header,
  .theme-toggle,
  .github-link,
  .project-links,
  .contact-links {
    display: none;
  }

  .project-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .hero {
    padding: var(--space-8) 0;
  }

  .projects,
  .contact {
    padding: var(--space-8) 0;
  }
}
