/**
 * Main Portfolio Functionality Tests
 * Tests the actual main.js code functionality
 */

const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

describe('Main Portfolio Functionality', () => {
  let dom;
  let window;
  let document;

  beforeAll(() => {
    // Load actual HTML and JS files
    const htmlPath = path.join(__dirname, '../index.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf8');
    
    dom = new JSDOM(htmlContent, {
      url: 'http://localhost',
      pretendToBeVisual: true,
      resources: 'usable'
    });
    
    window = dom.window;
    document = window.document;
    
    // Mock necessary browser APIs
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn()
      },
      writable: true
    });

    Object.defineProperty(window, 'matchMedia', {
      value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
      writable: true
    });

    // Mock gtag
    window.gtag = jest.fn();

    // Mock IntersectionObserver
    window.IntersectionObserver = jest.fn().mockImplementation(() => ({
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn()
    }));

    // Load and execute main.js
    const mainJsPath = path.join(__dirname, '../main.js');
    const mainJsContent = fs.readFileSync(mainJsPath, 'utf8');
    
    const script = document.createElement('script');
    script.textContent = mainJsContent;
    document.head.appendChild(script);
  });

  describe('DOM Elements Existence', () => {
    test('should have all required elements in DOM', () => {
      // Check main navigation elements
      expect(document.querySelector('.hero-title')).toBeTruthy();
      expect(document.querySelector('.hero-description')).toBeTruthy();
      
      // Check project cards
      const projectCards = document.querySelectorAll('.project-card');
      expect(projectCards.length).toBeGreaterThan(0);
      
      // Check contact modal
      expect(document.getElementById('contactModal')).toBeTruthy();
      expect(document.getElementById('emailContactBtn')).toBeTruthy();
    });

    test('should have correct project count', () => {
      const projectCards = document.querySelectorAll('.project-card');
      expect(projectCards.length).toBe(6); // 3 frontend + 3 backend
    });

    test('should have working demo links', () => {
      const demoLinks = document.querySelectorAll('.btn-primary');
      expect(demoLinks.length).toBeGreaterThan(0);
      
      demoLinks.forEach(link => {
        expect(link.href).toBeTruthy();
        expect(link.href).not.toBe('');
      });
    });
  });

  describe('Theme Functionality', () => {
    test('should initialize theme system', () => {
      const themeToggle = document.querySelector('.theme-toggle');
      expect(themeToggle).toBeTruthy();
      
      const themeIcon = document.querySelector('.theme-icon');
      expect(themeIcon).toBeTruthy();
    });

    test('should handle theme toggle clicks', () => {
      const themeToggle = document.querySelector('.theme-toggle');
      const initialTheme = document.documentElement.getAttribute('data-theme');
      
      // Simulate click
      themeToggle.click();
      
      // Should have attempted to change theme
      expect(window.localStorage.setItem).toHaveBeenCalled();
    });

    test('should respect saved theme preference', () => {
      window.localStorage.getItem.mockReturnValue('dark');
      
      // Simulate theme initialization
      const savedTheme = window.localStorage.getItem('portfolio-theme');
      expect(savedTheme).toBe('dark');
    });
  });

  describe('Contact Modal', () => {
    test('should open contact modal', () => {
      const emailBtn = document.getElementById('emailContactBtn');
      const modal = document.getElementById('contactModal');
      
      expect(emailBtn).toBeTruthy();
      expect(modal).toBeTruthy();
      
      // Initial state should be hidden
      expect(modal.style.display).toBe('none');
      
      // Simulate click
      emailBtn.click();
      
      // Modal should be visible (this depends on the actual implementation)
      // We're testing that the elements exist and can be interacted with
    });

    test('should close contact modal', () => {
      const closeBtn = document.getElementById('closeContactModal');
      const modal = document.getElementById('contactModal');
      
      expect(closeBtn).toBeTruthy();
      
      // Simulate close
      closeBtn.click();
      
      // Should attempt to hide modal
      expect(modal).toBeTruthy();
    });

    test('should have copy email functionality', () => {
      const copyBtn = document.getElementById('copyEmailBtn');
      expect(copyBtn).toBeTruthy();
      
      // Mock clipboard API
      Object.defineProperty(window.navigator, 'clipboard', {
        value: {
          writeText: jest.fn().mockResolvedValue()
        },
        writable: true
      });
      
      // Simulate copy
      copyBtn.click();
      
      // Should have attempted to copy (implementation dependent)
    });
  });

  describe('Project Cards', () => {
    test('should have correct project information', () => {
      const projectCards = document.querySelectorAll('.project-card');
      
      projectCards.forEach(card => {
        // Each card should have a title
        const title = card.querySelector('.project-title, h3');
        expect(title).toBeTruthy();
        expect(title.textContent.trim()).not.toBe('');
        
        // Each card should have a description
        const description = card.querySelector('.project-description, p');
        expect(description).toBeTruthy();
        expect(description.textContent.trim()).not.toBe('');
        
        // Each card should have tech tags or demo link
        const techTags = card.querySelectorAll('.tech');
        const demoLink = card.querySelector('.btn-primary');
        expect(techTags.length > 0 || demoLink).toBeTruthy();
      });
    });

    test('should have keyboard navigation support', () => {
      const projectCards = document.querySelectorAll('.project-card');
      
      projectCards.forEach(card => {
        // Cards should be focusable or contain focusable elements
        const isFocusable = card.tabIndex >= 0 || 
                           card.querySelector('a, button, [tabindex]');
        expect(isFocusable).toBeTruthy();
      });
    });
  });

  describe('Performance and Accessibility', () => {
    test('should have proper semantic HTML', () => {
      // Check for semantic elements
      expect(document.querySelector('header')).toBeTruthy();
      expect(document.querySelector('main')).toBeTruthy();
      expect(document.querySelector('footer')).toBeTruthy();
      
      // Check for proper heading hierarchy
      const h1 = document.querySelector('h1');
      expect(h1).toBeTruthy();
      
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      expect(headings.length).toBeGreaterThan(1);
    });

    test('should have alt text for images', () => {
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        expect(img.alt).toBeDefined();
        // Alt can be empty for decorative images, but should be defined
      });
    });

    test('should have proper ARIA labels where needed', () => {
      const buttons = document.querySelectorAll('button');
      buttons.forEach(button => {
        // Buttons should have accessible text or aria-label
        const hasAccessibleText = button.textContent.trim() !== '' || 
                                 button.getAttribute('aria-label') ||
                                 button.getAttribute('title');
        expect(hasAccessibleText).toBeTruthy();
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle missing localStorage gracefully', () => {
      // Temporarily break localStorage
      const originalLocalStorage = window.localStorage;
      delete window.localStorage;
      
      // Should not throw errors when localStorage is unavailable
      expect(() => {
        // Simulate theme toggle without localStorage
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
          themeToggle.click();
        }
      }).not.toThrow();
      
      // Restore localStorage
      window.localStorage = originalLocalStorage;
    });

    test('should handle missing DOM elements gracefully', () => {
      // Test that code doesn't break when expected elements are missing
      const nonExistentElement = document.getElementById('nonExistentElement');
      expect(nonExistentElement).toBeNull();
      
      // Code should handle null elements without throwing
      expect(() => {
        if (nonExistentElement) {
          nonExistentElement.click();
        }
      }).not.toThrow();
    });
  });

  describe('Analytics Integration', () => {
    test('should have gtag function available', () => {
      expect(window.gtag).toBeDefined();
      expect(typeof window.gtag).toBe('function');
    });

    test('should track events when gtag is available', () => {
      // Simulate an event that should trigger analytics
      const demoLink = document.querySelector('.btn-primary');
      if (demoLink) {
        demoLink.click();
        
        // Should have called gtag (implementation dependent)
        // This tests that gtag is available and can be called
        expect(window.gtag).toBeDefined();
      }
    });
  });
});
