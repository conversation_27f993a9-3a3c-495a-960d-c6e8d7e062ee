/**
 * Test Setup Configuration
 * Global test utilities and mocks for portfolio testing
 */

// Polyfill for TextEncoder/TextDecoder (required for JSDOM)
import { TextEncoder, TextDecoder } from 'util';
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock browser APIs that aren't available in Node.js test environment
global.matchMedia =
  global.matchMedia ||
  function (query) {
    return {
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn()
    };
  };

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
global.localStorage = localStorageMock;

// Mock sessionStorage
global.sessionStorage = localStorageMock;

// Mock navigator.clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn().mockResolvedValue(),
    readText: jest.fn().mockResolvedValue('')
  }
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock performance API
global.performance = {
  ...global.performance,
  getEntriesByType: jest.fn().mockReturnValue([
    {
      loadEventEnd: 1000,
      loadEventStart: 900,
      domContentLoadedEventEnd: 800,
      domContentLoadedEventStart: 700
    }
  ]),
  getEntriesByName: jest.fn().mockReturnValue([]),
  mark: jest.fn(),
  measure: jest.fn(),
  now: jest.fn().mockReturnValue(Date.now())
};

// Mock console methods for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole;
};

// Mock window.requestAnimationFrame
global.requestAnimationFrame = jest.fn().mockImplementation(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn().mockImplementation(id => clearTimeout(id));

// Mock CSS.supports
global.CSS = {
  supports: jest.fn().mockReturnValue(true)
};

// Test utilities
global.testUtils = {
  // Create a mock DOM element
  createElement: (tag, attributes = {}, children = []) => {
    const element = document.createElement(tag);
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });
    children.forEach(child => {
      if (typeof child === 'string') {
        element.textContent = child;
      } else {
        element.appendChild(child);
      }
    });
    return element;
  },

  // Simulate user events
  fireEvent: (element, eventType, eventInit = {}) => {
    const event = new Event(eventType, { bubbles: true, ...eventInit });
    element.dispatchEvent(event);
    return event;
  },

  // Wait for async operations
  waitFor: (callback, timeout = 1000) => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        try {
          const result = callback();
          if (result) {
            resolve(result);
          } else if (Date.now() - startTime >= timeout) {
            reject(new Error('Timeout waiting for condition'));
          } else {
            setTimeout(check, 10);
          }
        } catch (error) {
          if (Date.now() - startTime >= timeout) {
            reject(error);
          } else {
            setTimeout(check, 10);
          }
        }
      };
      check();
    });
  },

  // Mock fetch for API testing
  mockFetch: (response, options = {}) => {
    const mockResponse = {
      ok: true,
      status: 200,
      json: jest.fn().mockResolvedValue(response),
      text: jest.fn().mockResolvedValue(JSON.stringify(response)),
      ...options
    };
    global.fetch = jest.fn().mockResolvedValue(mockResponse);
    return mockResponse;
  }
};

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset localStorage
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();

  // Clean up DOM
  document.body.innerHTML = '';
  document.head.innerHTML = '';

  // Reset document attributes
  document.documentElement.removeAttribute('data-theme');
  document.documentElement.removeAttribute('class');
});

// Global test configuration
jest.setTimeout(10000); // 10 second timeout for all tests
