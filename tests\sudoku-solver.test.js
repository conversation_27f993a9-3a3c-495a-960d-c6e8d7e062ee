/**
 * Sudoku Solver Tests
 * Tests the actual backtracking algorithm implementation
 */

describe('SudokuSolver', () => {
  let SudokuSolver;

  beforeAll(() => {
    // Load the SudokuSolver from the frontend project
    const fs = require('fs');
    const path = require('path');
    
    // Read the sudoku solver file
    const solverPath = path.join(__dirname, '../projects/sudoku-solver/js/sudoku-solver.js');
    const solverCode = fs.readFileSync(solverPath, 'utf8');
    
    // Extract the SudokuSolver class (simple eval for testing)
    eval(solverCode);
    global.SudokuSolver = SudokuSolver;
  });

  let solver;

  beforeEach(() => {
    solver = new global.SudokuSolver();
  });

  describe('Validation', () => {
    test('should validate empty board', () => {
      const emptyBoard = Array(9).fill().map(() => Array(9).fill(0));
      expect(solver.isValidBoard(emptyBoard)).toBe(true);
    });

    test('should detect invalid row', () => {
      const board = Array(9).fill().map(() => Array(9).fill(0));
      board[0][0] = 5;
      board[0][1] = 5; // Duplicate in row
      
      expect(solver.isValidBoard(board)).toBe(false);
    });

    test('should detect invalid column', () => {
      const board = Array(9).fill().map(() => Array(9).fill(0));
      board[0][0] = 5;
      board[1][0] = 5; // Duplicate in column
      
      expect(solver.isValidBoard(board)).toBe(false);
    });

    test('should detect invalid 3x3 box', () => {
      const board = Array(9).fill().map(() => Array(9).fill(0));
      board[0][0] = 5;
      board[1][1] = 5; // Duplicate in same 3x3 box
      
      expect(solver.isValidBoard(board)).toBe(false);
    });

    test('should validate correct partial board', () => {
      const board = [
        [5, 3, 0, 0, 7, 0, 0, 0, 0],
        [6, 0, 0, 1, 9, 5, 0, 0, 0],
        [0, 9, 8, 0, 0, 0, 0, 6, 0],
        [8, 0, 0, 0, 6, 0, 0, 0, 3],
        [4, 0, 0, 8, 0, 3, 0, 0, 1],
        [7, 0, 0, 0, 2, 0, 0, 0, 6],
        [0, 6, 0, 0, 0, 0, 2, 8, 0],
        [0, 0, 0, 4, 1, 9, 0, 0, 5],
        [0, 0, 0, 0, 8, 0, 0, 7, 9]
      ];
      
      expect(solver.isValidBoard(board)).toBe(true);
    });
  });

  describe('Solving', () => {
    test('should solve easy puzzle', () => {
      const puzzle = [
        [5, 3, 0, 0, 7, 0, 0, 0, 0],
        [6, 0, 0, 1, 9, 5, 0, 0, 0],
        [0, 9, 8, 0, 0, 0, 0, 6, 0],
        [8, 0, 0, 0, 6, 0, 0, 0, 3],
        [4, 0, 0, 8, 0, 3, 0, 0, 1],
        [7, 0, 0, 0, 2, 0, 0, 0, 6],
        [0, 6, 0, 0, 0, 0, 2, 8, 0],
        [0, 0, 0, 4, 1, 9, 0, 0, 5],
        [0, 0, 0, 0, 8, 0, 0, 7, 9]
      ];

      const solution = solver.solve(puzzle);
      
      expect(solution).toBeTruthy();
      expect(solver.isValidBoard(puzzle)).toBe(true);
      expect(solver.isComplete(puzzle)).toBe(true);
    });

    test('should return false for unsolvable puzzle', () => {
      const unsolvable = [
        [5, 5, 0, 0, 0, 0, 0, 0, 0], // Invalid: two 5s in first row
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
      ];

      const solution = solver.solve(unsolvable);
      expect(solution).toBe(false);
    });

    test('should not modify original puzzle when solving fails', () => {
      const original = [
        [5, 5, 0, 0, 0, 0, 0, 0, 0], // Invalid puzzle
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
      ];

      const copy = original.map(row => [...row]);
      solver.solve(copy);
      
      // Original should be unchanged
      expect(original).toEqual([
        [5, 5, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
      ]);
    });
  });

  describe('Helper Methods', () => {
    test('should check if board is complete', () => {
      const incomplete = Array(9).fill().map(() => Array(9).fill(0));
      expect(solver.isComplete(incomplete)).toBe(false);

      const complete = [
        [5, 3, 4, 6, 7, 8, 9, 1, 2],
        [6, 7, 2, 1, 9, 5, 3, 4, 8],
        [1, 9, 8, 3, 4, 2, 5, 6, 7],
        [8, 5, 9, 7, 6, 1, 4, 2, 3],
        [4, 2, 6, 8, 5, 3, 7, 9, 1],
        [7, 1, 3, 9, 2, 4, 8, 5, 6],
        [9, 6, 1, 5, 3, 7, 2, 8, 4],
        [2, 8, 7, 4, 1, 9, 6, 3, 5],
        [3, 4, 5, 2, 8, 6, 1, 7, 9]
      ];
      expect(solver.isComplete(complete)).toBe(true);
    });

    test('should find empty cells', () => {
      const board = Array(9).fill().map(() => Array(9).fill(1));
      board[4][4] = 0; // Make one cell empty
      
      const emptyCell = solver.findEmptyCell(board);
      expect(emptyCell).toEqual([4, 4]);
    });

    test('should return null when no empty cells', () => {
      const board = Array(9).fill().map(() => Array(9).fill(1));
      
      const emptyCell = solver.findEmptyCell(board);
      expect(emptyCell).toBeNull();
    });

    test('should check if number is safe to place', () => {
      const board = Array(9).fill().map(() => Array(9).fill(0));
      board[0][0] = 5;
      
      // Should not be safe to place 5 in same row
      expect(solver.isSafe(board, 0, 1, 5)).toBe(false);
      
      // Should be safe to place different number
      expect(solver.isSafe(board, 0, 1, 3)).toBe(true);
    });
  });

  describe('Performance', () => {
    test('should solve puzzle within reasonable time', () => {
      const puzzle = [
        [0, 0, 0, 0, 0, 0, 6, 8, 0],
        [0, 0, 0, 0, 4, 6, 0, 0, 0],
        [7, 0, 0, 0, 0, 0, 0, 0, 9],
        [0, 5, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 1, 0, 0, 0, 0, 0],
        [0, 0, 0, 7, 0, 0, 0, 0, 0],
        [0, 0, 5, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
      ];

      const startTime = Date.now();
      const solved = solver.solve(puzzle);
      const endTime = Date.now();
      
      expect(solved).toBeTruthy();
      expect(endTime - startTime).toBeLessThan(5000); // Should solve within 5 seconds
    });
  });

  describe('Edge Cases', () => {
    test('should handle already solved puzzle', () => {
      const solved = [
        [5, 3, 4, 6, 7, 8, 9, 1, 2],
        [6, 7, 2, 1, 9, 5, 3, 4, 8],
        [1, 9, 8, 3, 4, 2, 5, 6, 7],
        [8, 5, 9, 7, 6, 1, 4, 2, 3],
        [4, 2, 6, 8, 5, 3, 7, 9, 1],
        [7, 1, 3, 9, 2, 4, 8, 5, 6],
        [9, 6, 1, 5, 3, 7, 2, 8, 4],
        [2, 8, 7, 4, 1, 9, 6, 3, 5],
        [3, 4, 5, 2, 8, 6, 1, 7, 9]
      ];

      const result = solver.solve(solved);
      expect(result).toBe(true);
      expect(solver.isComplete(solved)).toBe(true);
    });

    test('should handle empty board', () => {
      const empty = Array(9).fill().map(() => Array(9).fill(0));
      
      const result = solver.solve(empty);
      expect(result).toBe(true);
      expect(solver.isComplete(empty)).toBe(true);
      expect(solver.isValidBoard(empty)).toBe(true);
    });
  });
});
