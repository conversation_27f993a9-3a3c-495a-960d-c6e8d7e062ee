/**
 * Wordle Game Logic Tests
 * Tests the actual game implementation, not mocks
 */

const { WordleGame, GameStatistics, WordValidator, DIFFICULTY_LEVELS } = require('../backend/wordle-api/src/types');

describe('WordleGame', () => {
  let game;

  beforeEach(() => {
    game = new WordleGame('HOUSE', DIFFICULTY_LEVELS.MEDIUM);
  });

  describe('Game Initialization', () => {
    test('should initialize with correct properties', () => {
      expect(game.targetWord).toBe('house');
      expect(game.difficulty).toBe(DIFFICULTY_LEVELS.MEDIUM);
      expect(game.status).toBe('in_progress');
      expect(game.currentGuess).toBe(0);
      expect(game.maxGuesses).toBe(6);
      expect(game.guesses).toEqual([]);
    });

    test('should generate unique game ID', () => {
      const game1 = new WordleGame('HOUSE');
      const game2 = new WordleGame('WORLD');
      expect(game1.id).not.toBe(game2.id);
      expect(game1.id).toMatch(/^game_\d+_[a-z0-9]+$/);
    });
  });

  describe('Guess Validation', () => {
    test('should correctly validate guess with all correct letters', () => {
      const result = game.validateGuess('house');
      expect(result).toHaveLength(5);
      result.forEach(letter => {
        expect(letter.status).toBe('correct');
      });
    });

    test('should correctly identify present letters in wrong positions', () => {
      const result = game.validateGuess('esuoh'); // HOUSE backwards
      expect(result[0]).toEqual({ letter: 'e', status: 'present' }); // E is in HOUSE but not first
      expect(result[1]).toEqual({ letter: 's', status: 'present' }); // S is in HOUSE but not second
      expect(result[2]).toEqual({ letter: 'u', status: 'correct' }); // U is correct position
      expect(result[3]).toEqual({ letter: 'o', status: 'present' }); // O is in HOUSE but not fourth
      expect(result[4]).toEqual({ letter: 'h', status: 'present' }); // H is in HOUSE but not fifth
    });

    test('should correctly identify absent letters', () => {
      const result = game.validateGuess('abcde');
      result.forEach(letter => {
        expect(letter.status).toBe('absent');
      });
    });

    test('should handle duplicate letters correctly', () => {
      // Target: HOUSE, Guess: LOOSE
      const result = game.validateGuess('loose');
      expect(result[0]).toEqual({ letter: 'l', status: 'absent' });
      expect(result[1]).toEqual({ letter: 'o', status: 'correct' }); // First O matches
      expect(result[2]).toEqual({ letter: 'o', status: 'absent' }); // Second O doesn't match (only one O in HOUSE)
      expect(result[3]).toEqual({ letter: 's', status: 'correct' }); // S matches
      expect(result[4]).toEqual({ letter: 'e', status: 'correct' }); // E matches
    });
  });

  describe('Game Flow', () => {
    test('should accept valid guesses and update state', () => {
      const result = game.makeGuess('world');
      
      expect(game.currentGuess).toBe(1);
      expect(game.guesses).toHaveLength(1);
      expect(result.guess).toHaveLength(5);
      expect(result.gameStatus).toBe('in_progress');
      expect(result.isGameOver).toBe(false);
    });

    test('should win game with correct guess', () => {
      const result = game.makeGuess('house');
      
      expect(game.status).toBe('won');
      expect(result.gameStatus).toBe('won');
      expect(result.isGameOver).toBe(true);
      expect(result.targetWord).toBe('house');
      expect(game.endTime).toBeDefined();
    });

    test('should lose game after max guesses', () => {
      // Make 6 wrong guesses
      for (let i = 0; i < 6; i++) {
        const result = game.makeGuess('wrong');
        if (i < 5) {
          expect(result.isGameOver).toBe(false);
        } else {
          expect(result.isGameOver).toBe(true);
          expect(result.gameStatus).toBe('lost');
          expect(result.targetWord).toBe('house');
        }
      }
      
      expect(game.status).toBe('lost');
      expect(game.endTime).toBeDefined();
    });

    test('should throw error when making guess on finished game', () => {
      game.makeGuess('house'); // Win the game
      
      expect(() => {
        game.makeGuess('world');
      }).toThrow('Game is already finished');
    });

    test('should throw error when exceeding max guesses', () => {
      // Fill up all guesses
      for (let i = 0; i < 6; i++) {
        game.makeGuess('wrong');
      }
      
      expect(() => {
        game.makeGuess('extra');
      }).toThrow('Game is already finished');
    });
  });

  describe('Game State', () => {
    test('should return correct game state', () => {
      game.makeGuess('world');
      game.makeGuess('place');
      
      const state = game.getGameState();
      
      expect(state.id).toBe(game.id);
      expect(state.status).toBe('in_progress');
      expect(state.guesses).toHaveLength(2);
      expect(state.currentGuess).toBe(2);
      expect(state.maxGuesses).toBe(6);
      expect(state.difficulty).toBe(DIFFICULTY_LEVELS.MEDIUM);
      expect(state.startTime).toBeDefined();
      expect(state.endTime).toBeNull();
    });

    test('should calculate duration when game ends', () => {
      const startTime = game.startTime;
      
      // Wait a tiny bit to ensure duration > 0
      setTimeout(() => {
        game.makeGuess('house'); // Win the game
        
        const state = game.getGameState();
        expect(state.duration).toBeGreaterThan(0);
        expect(state.endTime).toBeDefined();
      }, 1);
    });
  });
});

describe('GameStatistics', () => {
  let stats;

  beforeEach(() => {
    stats = new GameStatistics();
  });

  test('should initialize with zero stats', () => {
    const initialStats = stats.getStats();
    
    expect(initialStats.totalGames).toBe(0);
    expect(initialStats.gamesWon).toBe(0);
    expect(initialStats.gamesLost).toBe(0);
    expect(initialStats.currentStreak).toBe(0);
    expect(initialStats.maxStreak).toBe(0);
    expect(initialStats.winRate).toBe('0.0');
  });

  test('should update stats for won game', () => {
    const game = new WordleGame('HOUSE');
    game.makeGuess('house'); // Win in 1 guess
    
    stats.updateStats(game);
    const result = stats.getStats();
    
    expect(result.totalGames).toBe(1);
    expect(result.gamesWon).toBe(1);
    expect(result.gamesLost).toBe(0);
    expect(result.currentStreak).toBe(1);
    expect(result.maxStreak).toBe(1);
    expect(result.winRate).toBe('100.0');
    expect(result.guessDistribution[1]).toBe(1);
  });

  test('should update stats for lost game', () => {
    const game = new WordleGame('HOUSE');
    // Lose the game
    for (let i = 0; i < 6; i++) {
      game.makeGuess('wrong');
    }
    
    stats.updateStats(game);
    const result = stats.getStats();
    
    expect(result.totalGames).toBe(1);
    expect(result.gamesWon).toBe(0);
    expect(result.gamesLost).toBe(1);
    expect(result.currentStreak).toBe(0);
    expect(result.maxStreak).toBe(0);
    expect(result.winRate).toBe('0.0');
  });

  test('should track streak correctly', () => {
    // Win 3 games
    for (let i = 0; i < 3; i++) {
      const game = new WordleGame('HOUSE');
      game.makeGuess('house');
      stats.updateStats(game);
    }
    
    expect(stats.getStats().currentStreak).toBe(3);
    expect(stats.getStats().maxStreak).toBe(3);
    
    // Lose a game
    const lostGame = new WordleGame('HOUSE');
    for (let i = 0; i < 6; i++) {
      lostGame.makeGuess('wrong');
    }
    stats.updateStats(lostGame);
    
    expect(stats.getStats().currentStreak).toBe(0);
    expect(stats.getStats().maxStreak).toBe(3); // Max streak preserved
  });
});

describe('WordValidator', () => {
  let validator;

  beforeEach(() => {
    validator = new WordValidator();
  });

  test('should validate common words', () => {
    expect(validator.isValidWord('house')).toBe(true);
    expect(validator.isValidWord('world')).toBe(true);
    expect(validator.isValidWord('about')).toBe(true);
  });

  test('should reject invalid words', () => {
    expect(validator.isValidWord('zzzzz')).toBe(false);
    expect(validator.isValidWord('12345')).toBe(false);
    expect(validator.isValidWord('')).toBe(false);
  });

  test('should be case insensitive', () => {
    expect(validator.isValidWord('HOUSE')).toBe(true);
    expect(validator.isValidWord('House')).toBe(true);
    expect(validator.isValidWord('hOuSe')).toBe(true);
  });

  test('should add new words', () => {
    expect(validator.isValidWord('testword')).toBe(false);
    
    validator.addWord('testword');
    expect(validator.isValidWord('testword')).toBe(true);
  });

  test('should add multiple words', () => {
    const newWords = ['word1', 'word2', 'word3'];
    validator.addWords(newWords);
    
    newWords.forEach(word => {
      expect(validator.isValidWord(word)).toBe(true);
    });
  });

  test('should get random words', () => {
    const word1 = validator.getRandomWord();
    const word2 = validator.getRandomWord();
    
    expect(typeof word1).toBe('string');
    expect(word1.length).toBeGreaterThan(0);
    expect(validator.isValidWord(word1)).toBe(true);
    
    // Should be able to get different words (though not guaranteed)
    expect(typeof word2).toBe('string');
  });
});

describe('DIFFICULTY_LEVELS', () => {
  test('should have correct difficulty constants', () => {
    expect(DIFFICULTY_LEVELS.EASY).toBe('easy');
    expect(DIFFICULTY_LEVELS.MEDIUM).toBe('medium');
    expect(DIFFICULTY_LEVELS.HARD).toBe('hard');
  });
});
